# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: proto.proto
# Protobuf Python Version: 5.28.3
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    28,
    3,
    '',
    'proto.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0bproto.proto\"\xa4\x02\n\x05\x46rame\x12\x12\n\x05seqid\x18\x01 \x01(\x04H\x00\x88\x01\x01\x12\x12\n\x05logid\x18\x02 \x01(\x04H\x01\x88\x01\x01\x12\x14\n\x07service\x18\x03 \x01(\x05H\x02\x88\x01\x01\x12\x13\n\x06method\x18\x04 \x01(\x05H\x03\x88\x01\x01\x12\x1f\n\x07headers\x18\x05 \x03(\x0b\x32\x0e.ExtendedEntry\x12\x1c\n\x0fpayloadEncoding\x18\x06 \x01(\tH\x04\x88\x01\x01\x12\x18\n\x0bpayloadType\x18\x07 \x01(\tH\x05\x88\x01\x01\x12\x14\n\x07payload\x18\x08 \x01(\x0cH\x06\x88\x01\x01\x42\x08\n\x06_seqidB\x08\n\x06_logidB\n\n\x08_serviceB\t\n\x07_methodB\x12\n\x10_payloadEncodingB\x0e\n\x0c_payloadTypeB\n\n\x08_payload\"\xca\x03\n\x06Packet\x12\x10\n\x03sid\x18\x01 \x01(\tH\x00\x88\x01\x01\x12&\n\x0bpayloadType\x18\x02 \x01(\x0e\x32\x0c.PayloadTypeH\x01\x88\x01\x01\x12\x10\n\x03\x63md\x18\x03 \x01(\x05H\x02\x88\x01\x01\x12\x13\n\x06status\x18\x04 \x01(\rH\x03\x88\x01\x01\x12\x14\n\x07payload\x18\x05 \x01(\x0cH\x04\x88\x01\x01\x12\x10\n\x03\x63id\x18\x06 \x01(\tH\x05\x88\x01\x01\x12$\n\npipeEntity\x18\x07 \x01(\x0b\x32\x0b.PipeEntityH\x06\x88\x01\x01\x12(\n\x0fversionPayloads\x18\x08 \x03(\x0b\x32\x0f.VersionPayload\x12!\n\x0cpipeEntities\x18\t \x03(\x0b\x32\x0b.PipeEntity\x12\x1e\n\x11waitRetryInterval\x18\n \x01(\rH\x07\x88\x01\x01\x12\x14\n\x07\x63ommand\x18\x0b \x01(\x05H\x08\x88\x01\x01\x12\x13\n\x06\x63ursor\x18\x0c \x01(\x04H\t\x88\x01\x01\x42\x06\n\x04_sidB\x0e\n\x0c_payloadTypeB\x06\n\x04_cmdB\t\n\x07_statusB\n\n\x08_payloadB\x06\n\x04_cidB\r\n\x0b_pipeEntityB\x14\n\x12_waitRetryIntervalB\n\n\x08_commandB\t\n\x07_cursor\"\xfd\x03\n\x13PushMessagesRequest\x12\x34\n\x08messages\x18\x01 \x03(\x0b\x32\".PushMessagesRequest.MessagesEntry\x12P\n\x16participatedMessageIds\x18\x03 \x03(\x0b\x32\x30.PushMessagesRequest.ParticipatedMessageIdsEntry\x12\x36\n\tforcePush\x18\x08 \x03(\x0b\x32#.PushMessagesRequest.ForcePushEntry\x12<\n\x0cmessagesAtMe\x18\t \x03(\x0b\x32&.PushMessagesRequest.MessagesAtMeEntry\x1a\x42\n\rMessagesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12 \n\x05value\x18\x02 \x01(\x0b\x32\x11.entities.Message:\x02\x38\x01\x1a=\n\x1bParticipatedMessageIdsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x08:\x02\x38\x01\x1a\x30\n\x0e\x46orcePushEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x08:\x02\x38\x01\x1a\x33\n\x11MessagesAtMeEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x08:\x02\x38\x01\"\x82\x04\n\x11PutMessageRequest\x12\x18\n\x04type\x18\x01 \x01(\x0e\x32\x05.TypeH\x00\x88\x01\x01\x12\x1e\n\x07\x63ontent\x18\x02 \x01(\x0b\x32\x08.ContentH\x01\x88\x01\x01\x12\x13\n\x06\x63hatId\x18\x03 \x01(\tH\x02\x88\x01\x01\x12\x13\n\x06rootId\x18\x04 \x01(\tH\x03\x88\x01\x01\x12\x15\n\x08parentId\x18\x05 \x01(\tH\x04\x88\x01\x01\x12\x10\n\x03\x63id\x18\x06 \x01(\tH\x05\x88\x01\x01\x12\x17\n\nisNotified\x18\x07 \x01(\x08H\x06\x88\x01\x01\x12\x17\n\nsendToChat\x18\x08 \x01(\x08H\x07\x88\x01\x01\x12\x14\n\x07version\x18\t \x01(\x05H\x08\x88\x01\x01\x12\x1f\n\x12isThreadGroupTopic\x18\n \x01(\x08H\t\x88\x01\x01\x12\x18\n\x0bisAnonymous\x18\x0b \x01(\x08H\n\x88\x01\x01\x12\x1e\n\x16leftStaticResourceKeys\x18\x65 \x03(\t\x12\x1a\n\rthriftMessage\x18\x66 \x01(\x0cH\x0b\x88\x01\x01\x42\x07\n\x05_typeB\n\n\x08_contentB\t\n\x07_chatIdB\t\n\x07_rootIdB\x0b\n\t_parentIdB\x06\n\x04_cidB\r\n\x0b_isNotifiedB\r\n\x0b_sendToChatB\n\n\x08_versionB\x15\n\x13_isThreadGroupTopicB\x0e\n\x0c_isAnonymousB\x10\n\x0e_thriftMessage\"\x81\x07\n\x0ePutChatRequest\x12\x18\n\x04type\x18\x01 \x01(\x0e\x32\x05.TypeH\x00\x88\x01\x01\x12\x0f\n\x07userIds\x18\x02 \x03(\t\x12\x16\n\tgroupName\x18\x03 \x01(\tH\x01\x88\x01\x01\x12\x16\n\tgroupDesc\x18\x04 \x01(\tH\x02\x88\x01\x01\x12\x15\n\x08isPublic\x18\x05 \x01(\x08H\x03\x88\x01\x01\x12\x12\n\nchatterIds\x18\x06 \x03(\t\x12\x1b\n\x0eorganizationId\x18\x07 \x01(\tH\x04\x88\x01\x01\x12\x17\n\nfromChatId\x18\x08 \x01(\tH\x05\x88\x01\x01\x12\x16\n\x0einitMessageIds\x18\t \x03(\t\x12\x14\n\x07iconKey\x18\n \x01(\tH\x06\x88\x01\x01\x12/\n\x08\x64ocPerms\x18\x0b \x03(\x0b\x32\x1d.PutChatRequest.DocPermsEntry\x12\x1a\n\risCrossTenant\x18\x0c \x01(\x08H\x07\x88\x01\x01\x12\x17\n\nisPublicV2\x18\r \x01(\x08H\x08\x88\x01\x01\x12\x31\n\tdocPerms2\x18\x0f \x03(\x0b\x32\x1e.PutChatRequest.DocPerms2Entry\x12\x10\n\x03\x63id\x18\x10 \x01(\tH\t\x88\x01\x01\x1aL\n\rDocPermsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12*\n\x05value\x18\x02 \x01(\x0e\x32\x1b.PutChatRequest.DocPermType:\x02\x38\x01\x1aI\n\x0e\x44ocPerms2Entry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12&\n\x05value\x18\x02 \x01(\x0b\x32\x17.PutChatRequest.DocPair:\x02\x38\x01\x1a\x87\x01\n\x07\x44ocPair\x12\x31\n\x05perms\x18\x01 \x03(\x0b\x32\".PutChatRequest.DocPair.PermsEntry\x1aI\n\nPermsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12*\n\x05value\x18\x02 \x01(\x0e\x32\x1b.PutChatRequest.DocPermType:\x02\x38\x01\".\n\x0b\x44ocPermType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x08\n\x04READ\x10\x01\x12\x08\n\x04\x45\x44IT\x10\x02\x42\x07\n\x05_typeB\x0c\n\n_groupNameB\x0c\n\n_groupDescB\x0b\n\t_isPublicB\x11\n\x0f_organizationIdB\r\n\x0b_fromChatIdB\n\n\x08_iconKeyB\x10\n\x0e_isCrossTenantB\r\n\x0b_isPublicV2B\x06\n\x04_cid\"c\n\x0fPutChatResponse\x12\x18\n\x04\x63hat\x18\x01 \x01(\x0b\x32\x05.ChatH\x00\x88\x01\x01\x12 \n\x08\x66\x65\x65\x64\x43\x61rd\x18\x02 \x01(\x0b\x32\t.FeedCardH\x01\x88\x01\x01\x42\x07\n\x05_chatB\x0b\n\t_feedCard\"T\n\x16UniversalSearchRequest\x12/\n\x06header\x18\x01 \x01(\x0b\x32\x1a.SearchCommonRequestHeaderH\x00\x88\x01\x01\x42\t\n\x07_header\"\xac\x16\n\x17UniversalSearchResponse\x12H\n\x06header\x18\x01 \x01(\x0b\x32\x33.UniversalSearchResponse.SearchCommonResponseHeaderH\x00\x88\x01\x01\x12\x36\n\x07results\x18\x02 \x03(\x0b\x32%.UniversalSearchResponse.SearchResult\x12\x44\n\x0b\x65xtraFields\x18\x03 \x01(\x0b\x32*.UniversalSearchResponse.SearchExtraFieldsH\x01\x88\x01\x01\x12\x44\n\x11\x66\x61iledEntityInfos\x18\x05 \x03(\x0b\x32).UniversalSearchResponse.FailedEntityInfo\x1a\xed\x02\n\x0cSearchResult\x12\x0f\n\x02id\x18\x01 \x01(\tH\x00\x88\x01\x01\x12<\n\x04type\x18\x02 \x01(\x0e\x32).UniversalSearchResponse.SearchEntityTypeH\x01\x88\x01\x01\x12\x1d\n\x10titleHighlighted\x18\x03 \x01(\tH\x02\x88\x01\x01\x12\x1f\n\x12summaryHighlighted\x18\x04 \x01(\tH\x03\x88\x01\x01\x12\x1e\n\x11\x65xtrasHighlighted\x18\x05 \x01(\tH\x04\x88\x01\x01\x12\x16\n\tavatarKey\x18\x06 \x01(\tH\x05\x88\x01\x01\x12\x1f\n\x12\x65xtraInfoSeparator\x18\n \x01(\tH\x06\x88\x01\x01\x42\x05\n\x03_idB\x07\n\x05_typeB\x13\n\x11_titleHighlightedB\x15\n\x13_summaryHighlightedB\x14\n\x12_extrasHighlightedB\x0c\n\n_avatarKeyB\x15\n\x13_extraInfoSeparator\x1a\x8f\t\n\x1aSearchCommonResponseHeader\x12\x1a\n\rsearchSession\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x19\n\x0csessionSeqId\x18\x02 \x01(\x05H\x01\x88\x01\x01\x12\x12\n\x05total\x18\x03 \x01(\x05H\x02\x88\x01\x01\x12\x14\n\x07hasMore\x18\x04 \x01(\x08H\x03\x88\x01\x01\x12\x1c\n\x0fpaginationToken\x18\x05 \x01(\tH\x04\x88\x01\x01\x12k\n\x14invokeAbnormalNotice\x18\x06 \x01(\x0e\x32H.UniversalSearchResponse.SearchCommonResponseHeader.InvokeAbnormalNoticeH\x05\x88\x01\x01\x12\x63\n\x0bstorageInfo\x18\x07 \x01(\x0b\x32I.UniversalSearchResponse.SearchCommonResponseHeader.ColdAndHotStorageInfoH\x06\x88\x01\x01\x1a\xb9\x04\n\x15\x43oldAndHotStorageInfo\x12\x1b\n\x0eisNeedColdData\x18\x01 \x01(\x08H\x00\x88\x01\x01\x12\x15\n\x08timeSize\x18\x02 \x01(\x05H\x01\x88\x01\x01\x12i\n\x08timeUnit\x18\x03 \x01(\x0e\x32R.UniversalSearchResponse.SearchCommonResponseHeader.ColdAndHotStorageInfo.TimeUnitH\x02\x88\x01\x01\x12\x15\n\x08timeText\x18\x04 \x01(\tH\x03\x88\x01\x01\x12o\n\x0bhasMoreInfo\x18\x05 \x01(\x0e\x32U.UniversalSearchResponse.SearchCommonResponseHeader.ColdAndHotStorageInfo.HasMoreInfoH\x04\x88\x01\x01\"C\n\x08TimeUnit\x12\x0f\n\x0bTimeUNKNOWN\x10\x00\x12\x07\n\x03\x44\x41Y\x10\x01\x12\x08\n\x04WEEK\x10\x02\x12\t\n\x05MONTH\x10\x03\x12\x08\n\x04YEAR\x10\x04\"j\n\x0bHasMoreInfo\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x10\n\x0cHOT_HAS_MORE\x10\x01\x12\x13\n\x0fHOT_HAS_NO_MORE\x10\x02\x12\x11\n\rCOLD_HAS_MORE\x10\x03\x12\x14\n\x10\x43OLD_HAS_NO_MORE\x10\x04\x42\x11\n\x0f_isNeedColdDataB\x0b\n\t_timeSizeB\x0b\n\t_timeUnitB\x0b\n\t_timeTextB\x0e\n\x0c_hasMoreInfo\"n\n\x14InvokeAbnormalNotice\x12\x11\n\rINVOKE_NORMAL\x10\x00\x12\x14\n\x10REQUEST_CANCELED\x10\x01\x12\x12\n\x0eVERSION_SWITCH\x10\x02\x12\x19\n\x15QUERY_LENGTH_EXCEEDED\x10\x03\x42\x10\n\x0e_searchSessionB\x0f\n\r_sessionSeqIdB\x08\n\x06_totalB\n\n\x08_hasMoreB\x12\n\x10_paginationTokenB\x17\n\x15_invokeAbnormalNoticeB\x0e\n\x0c_storageInfo\x1aY\n\x11SearchExtraFields\x12&\n\x19\x63hatterPermissionResponse\x18\x01 \x01(\x0cH\x00\x88\x01\x01\x42\x1c\n\x1a_chatterPermissionResponse\x1a\xbb\x04\n\x10\x46\x61iledEntityInfo\x12S\n\nentityType\x18\x01 \x01(\x0e\x32:.UniversalSearchResponse.FailedEntityInfo.SearchEntityTypeH\x00\x88\x01\x01\x12 \n\x13isNeedLocalFallback\x18\x02 \x01(\x08H\x01\x88\x01\x01\x12!\n\x14localPaginationToken\x18\x03 \x01(\tH\x02\x88\x01\x01\"\xcc\x02\n\x10SearchEntityType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x08\n\x04USER\x10\x01\x12\x07\n\x03\x42OT\x10\x02\x12\x0e\n\nGROUP_CHAT\x10\x03\x12\x13\n\x0f\x43RYPTO_P2P_CHAT\x10\x04\x12\x0b\n\x07MESSAGE\x10\x05\x12\x07\n\x03\x44OC\x10\x07\x12\x08\n\x04WIKI\x10\x08\x12\x07\n\x03\x41PP\x10\t\x12\n\n\x06ONCALL\x10\n\x12\n\n\x06THREAD\x10\x0b\x12\x0b\n\x07QA_CARD\x10\x0c\x12\x07\n\x03URL\x10\r\x12\x0e\n\nDEPARTMENT\x10\x0e\x12\x08\n\x04PANO\x10\x0f\x12\x11\n\rSLASH_COMMAND\x10\x10\x12\x0b\n\x07SECTION\x10\x11\x12\x0c\n\x08RESOURCE\x10\x12\x12\x11\n\rCUSTOMIZATION\x10\x13\x12\x0c\n\x08\x46\x41\x43ILITY\x10\x14\x12\x10\n\x0cMAIL_CONTACT\x10\x15\x12\r\n\tCHAMELEON\x10\x16\x12\x12\n\x0e\x43\x41LENDAR_EVENT\x10\x17\x42\r\n\x0b_entityTypeB\x16\n\x14_isNeedLocalFallbackB\x17\n\x15_localPaginationToken\"\xcc\x02\n\x10SearchEntityType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x08\n\x04USER\x10\x01\x12\x07\n\x03\x42OT\x10\x02\x12\x0e\n\nGROUP_CHAT\x10\x03\x12\x13\n\x0f\x43RYPTO_P2P_CHAT\x10\x04\x12\x0b\n\x07MESSAGE\x10\x05\x12\x07\n\x03\x44OC\x10\x07\x12\x08\n\x04WIKI\x10\x08\x12\x07\n\x03\x41PP\x10\t\x12\n\n\x06ONCALL\x10\n\x12\n\n\x06THREAD\x10\x0b\x12\x0b\n\x07QA_CARD\x10\x0c\x12\x07\n\x03URL\x10\r\x12\x0e\n\nDEPARTMENT\x10\x0e\x12\x08\n\x04PANO\x10\x0f\x12\x11\n\rSLASH_COMMAND\x10\x10\x12\x0b\n\x07SECTION\x10\x11\x12\x0c\n\x08RESOURCE\x10\x12\x12\x11\n\rCUSTOMIZATION\x10\x13\x12\x0c\n\x08\x46\x41\x43ILITY\x10\x14\x12\x10\n\x0cMAIL_CONTACT\x10\x15\x12\r\n\tCHAMELEON\x10\x16\x12\x12\n\x0e\x43\x41LENDAR_EVENT\x10\x17\x42\t\n\x07_headerB\x0e\n\x0c_extraFields\"\xfa\x05\n\x19SearchCommonRequestHeader\x12\x1a\n\rsearchSession\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x19\n\x0csessionSeqId\x18\x02 \x01(\x05H\x01\x88\x01\x01\x12\x12\n\x05query\x18\x03 \x01(\tH\x02\x88\x01\x01\x12\x1c\n\x0fpaginationToken\x18\x04 \x01(\tH\x03\x88\x01\x01\x12\x35\n\rsearchContext\x18\x05 \x01(\x0b\x32\x19.BaseEntity.SearchContextH\x04\x88\x01\x01\x12\x13\n\x06locale\x18\x06 \x01(\tH\x05\x88\x01\x01\x12\x19\n\x0cimpressionId\x18\x07 \x01(\tH\x06\x88\x01\x01\x12*\n\nextraParam\x18\x08 \x01(\x0b\x32\x11.SearchExtraParamH\x07\x88\x01\x01\x12;\n\x0btitleLayout\x18\t \x01(\x0b\x32!.SearchCommonRequestHeader.LayoutH\x08\x88\x01\x01\x12=\n\rsummaryLayout\x18\n \x01(\x0b\x32!.SearchCommonRequestHeader.LayoutH\t\x88\x01\x01\x12\x15\n\x08pageSize\x18\x0b \x01(\x05H\n\x88\x01\x01\x12\x44\n\x14sectionSummaryLayout\x18\r \x01(\x0b\x32!.SearchCommonRequestHeader.LayoutH\x0b\x88\x01\x01\x1a\x42\n\x06Layout\x12\x11\n\x04line\x18\x01 \x01(\x05H\x00\x88\x01\x01\x12\x12\n\x05width\x18\x02 \x01(\x05H\x01\x88\x01\x01\x42\x07\n\x05_lineB\x08\n\x06_widthB\x10\n\x0e_searchSessionB\x0f\n\r_sessionSeqIdB\x08\n\x06_queryB\x12\n\x10_paginationTokenB\x10\n\x0e_searchContextB\t\n\x07_localeB\x0f\n\r_impressionIdB\r\n\x0b_extraParamB\x0e\n\x0c_titleLayoutB\x10\n\x0e_summaryLayoutB\x0b\n\t_pageSizeB\x17\n\x15_sectionSummaryLayout\"\x88\x01\n\x10SearchExtraParam\x12%\n\x18\x63hatterPermissionRequest\x18\x01 \x01(\x0cH\x00\x88\x01\x01\x12\x1c\n\x0fqueryInputState\x18\x02 \x01(\x05H\x01\x88\x01\x01\x42\x1b\n\x19_chatterPermissionRequestB\x12\n\x10_queryInputState\"\xc3\x01\n\nBaseEntity\x1a\xb4\x01\n\rSearchContext\x12\x14\n\x07tagName\x18\x01 \x01(\tH\x00\x88\x01\x01\x12 \n\x0b\x65ntityItems\x18\x02 \x03(\x0b\x32\x0b.EntityItem\x12(\n\x0c\x63ommonFilter\x18\x03 \x01(\x0b\x32\r.CommonFilterH\x01\x88\x01\x01\x12\x16\n\tsourceKey\x18\x05 \x01(\tH\x02\x88\x01\x01\x42\n\n\x08_tagNameB\x0f\n\r_commonFilterB\x0c\n\n_sourceKey\"f\n\x0c\x43ommonFilter\x12\x1f\n\x12includeOuterTenant\x18\x01 \x01(\x08H\x00\x88\x01\x01\x12\x13\n\x06\x63hatId\x18\x02 \x01(\tH\x01\x88\x01\x01\x42\x15\n\x13_includeOuterTenantB\t\n\x07_chatId\"\xa2\x02\n\nUserFilter\x12\x17\n\nisResigned\x18\x01 \x01(\x08H\x00\x88\x01\x01\x12\x18\n\x0bhaveChatter\x18\x02 \x01(\x08H\x01\x88\x01\x01\x12\x33\n\x0c\x63ustomFields\x18\x03 \x03(\x0b\x32\x1d.UserFilter.CustomFieldsEntry\x12\x14\n\x07\x65xclude\x18\x04 \x01(\x08H\x02\x88\x01\x01\x1a\x1d\n\x0b\x46ieldValues\x12\x0e\n\x06values\x18\x01 \x03(\t\x1aL\n\x11\x43ustomFieldsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12&\n\x05value\x18\x02 \x01(\x0b\x32\x17.UserFilter.FieldValues:\x02\x38\x01\x42\r\n\x0b_isResignedB\x0e\n\x0c_haveChatterB\n\n\x08_exclude\"\xbd\x01\n\x0fGroupChatFilter\x12 \n\x0bsearchTypes\x18\x01 \x03(\x0e\x32\x0b.SearchType\x12\x15\n\rchatMemberIds\x18\x02 \x03(\t\x12\x17\n\x0f\x65xcludedChatIds\x18\x03 \x03(\t\x12\x19\n\x0csearchCrypto\x18\x04 \x01(\x08H\x00\x88\x01\x01\x12\x1a\n\raddableAsUser\x18\x05 \x01(\x08H\x01\x88\x01\x01\x42\x0f\n\r_searchCryptoB\x10\n\x0e_addableAsUser\"\x8b\x05\n\nEntityItem\x12/\n\x04type\x18\x01 \x01(\x0e\x32\x1c.EntityItem.SearchEntityTypeH\x00\x88\x01\x01\x12-\n\x06\x66ilter\x18\x02 \x01(\x0b\x32\x18.EntityItem.EntityFilterH\x01\x88\x01\x01\x12\x14\n\x0c\x62oostChatIds\x18\x04 \x03(\t\x12!\n\x14localPaginationToken\x18\x05 \x01(\tH\x02\x88\x01\x01\x1ah\n\x0c\x45ntityFilter\x12!\n\nuserFilter\x18\x01 \x01(\x0b\x32\x0b.UserFilterH\x00\x12+\n\x0fgroupChatFilter\x18\x02 \x01(\x0b\x32\x10.GroupChatFilterH\x00\x42\x08\n\x06\x66ilter\"\xcc\x02\n\x10SearchEntityType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x08\n\x04USER\x10\x01\x12\x07\n\x03\x42OT\x10\x02\x12\x0e\n\nGROUP_CHAT\x10\x03\x12\x13\n\x0f\x43RYPTO_P2P_CHAT\x10\x04\x12\x0b\n\x07MESSAGE\x10\x05\x12\x07\n\x03\x44OC\x10\x07\x12\x08\n\x04WIKI\x10\x08\x12\x07\n\x03\x41PP\x10\t\x12\n\n\x06ONCALL\x10\n\x12\n\n\x06THREAD\x10\x0b\x12\x0b\n\x07QA_CARD\x10\x0c\x12\x07\n\x03URL\x10\r\x12\x0e\n\nDEPARTMENT\x10\x0e\x12\x08\n\x04PANO\x10\x0f\x12\x11\n\rSLASH_COMMAND\x10\x10\x12\x0b\n\x07SECTION\x10\x11\x12\x0c\n\x08RESOURCE\x10\x12\x12\x11\n\rCUSTOMIZATION\x10\x13\x12\x0c\n\x08\x46\x41\x43ILITY\x10\x14\x12\x10\n\x0cMAIL_CONTACT\x10\x15\x12\r\n\tCHAMELEON\x10\x16\x12\x12\n\x0e\x43\x41LENDAR_EVENT\x10\x17\x42\x07\n\x05_typeB\t\n\x07_filterB\x17\n\x15_localPaginationToken\"\xda\x07\n\x07\x43ontent\x12\x11\n\x04text\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x15\n\x08imageKey\x18\x02 \x01(\tH\x01\x88\x01\x01\x12\x1b\n\x0eisOriginSource\x18\x1f \x01(\x08H\x02\x88\x01\x01\x12\x12\n\x05title\x18\x03 \x01(\tH\x03\x88\x01\x01\x12\x13\n\x0b\x61ttachments\x18\x04 \x03(\t\x12\x17\n\nisNotified\x18\x05 \x01(\x08H\x04\x88\x01\x01\x12\x15\n\x08\x61udioKey\x18\x07 \x01(\tH\x05\x88\x01\x01\x12\x1a\n\raudioDuration\x18\x08 \x01(\x05H\x06\x88\x01\x01\x12\x13\n\x06\x63hatId\x18\t \x01(\tH\x07\x88\x01\x01\x12\x18\n\x0b\x63ryptoToken\x18\n \x01(\tH\x08\x88\x01\x01\x12\x14\n\x07\x66ileKey\x18\x06 \x01(\tH\t\x88\x01\x01\x12\x15\n\x08\x66ileName\x18\x0b \x01(\tH\n\x88\x01\x01\x12\x15\n\x08\x66ileMime\x18\x0c \x01(\tH\x0b\x88\x01\x01\x12\x15\n\x08\x66ileSize\x18\r \x01(\x03H\x0c\x88\x01\x01\x12\x32\n\rfileTransMode\x18\x1c \x01(\x0e\x32\x16.Content.FileTransModeH\r\x88\x01\x01\x12\x1b\n\x0esenderDeviceId\x18\x1d \x01(\tH\x0e\x88\x01\x01\x12 \n\x08richText\x18\x0e \x01(\x0b\x32\t.RichTextH\x0f\x88\x01\x01\x12\x15\n\x08\x64uration\x18\x0f \x01(\x05H\x10\x88\x01\x01\x12\x1b\n\x0e\x61ttendeesCount\x18\x11 \x01(\x05H\x11\x88\x01\x01\x12 \n\x13isGroupAnnouncement\x18\x12 \x01(\x08H\x12\x88\x01\x01\x12\x19\n\x0cstickerSetId\x18\x18 \x01(\tH\x13\x88\x01\x01\x12\x16\n\tstickerId\x18\x19 \x01(\tH\x14\x88\x01\x01\x12\x18\n\x0bshareUserId\x18\x1b \x01(\tH\x15\x88\x01\x01\"+\n\rFileTransMode\x12\x0b\n\x07UNKNOWN\x10\x00\x12\r\n\tLAN_TRANS\x10\x01\x42\x07\n\x05_textB\x0b\n\t_imageKeyB\x11\n\x0f_isOriginSourceB\x08\n\x06_titleB\r\n\x0b_isNotifiedB\x0b\n\t_audioKeyB\x10\n\x0e_audioDurationB\t\n\x07_chatIdB\x0e\n\x0c_cryptoTokenB\n\n\x08_fileKeyB\x0b\n\t_fileNameB\x0b\n\t_fileMimeB\x0b\n\t_fileSizeB\x10\n\x0e_fileTransModeB\x11\n\x0f_senderDeviceIdB\x0b\n\t_richTextB\x0b\n\t_durationB\x11\n\x0f_attendeesCountB\x16\n\x14_isGroupAnnouncementB\x0f\n\r_stickerSetIdB\x0c\n\n_stickerIdB\x0e\n\x0c_shareUserId\"\xc2\x16\n\x08\x65ntities\x1a\xd5\x13\n\x07Message\x12\x0f\n\x02id\x18\x01 \x01(\tH\x00\x88\x01\x01\x12)\n\x04type\x18\x02 \x01(\x0e\x32\x16.entities.Message.TypeH\x01\x88\x01\x01\x12\x13\n\x06\x66romId\x18\x03 \x01(\tH\x02\x88\x01\x01\x12\x17\n\ncreateTime\x18\x04 \x01(\x03H\x03\x88\x01\x01\x12\x14\n\x07\x63ontent\x18\x05 \x01(\x0cH\x04\x88\x01\x01\x12%\n\x06status\x18\x06 \x01(\x0e\x32\x10.entities.StatusH\x05\x88\x01\x01\x12)\n\x08\x66romType\x18\x07 \x01(\x0e\x32\x12.entities.FromTypeH\x06\x88\x01\x01\x12\x13\n\x06rootId\x18\x08 \x01(\tH\x07\x88\x01\x01\x12\x15\n\x08parentId\x18\t \x01(\tH\x08\x88\x01\x01\x12\x13\n\x06\x63hatId\x18\n \x01(\tH\t\x88\x01\x01\x12\x1b\n\x0elastModifyTime\x18\x0b \x01(\x03H\n\x88\x01\x01\x12\x10\n\x03\x63id\x18\x0c \x01(\tH\x0b\x88\x01\x01\x12\x15\n\x08position\x18\r \x01(\x05H\x0c\x88\x01\x01\x12\x17\n\nupdateTime\x18\x0e \x01(\x03H\r\x88\x01\x01\x12\x17\n\nisNotified\x18\x0f \x01(\x08H\x0e\x88\x01\x01\x12\x17\n\nreplyCount\x18\x10 \x01(\tH\x0f\x88\x01\x01\x12\"\n\x15parentSourceMessageId\x18\x11 \x01(\tH\x10\x88\x01\x01\x12 \n\x13rootSourceMessageId\x18\x12 \x01(\tH\x11\x88\x01\x01\x12\x13\n\x06isDing\x18\x13 \x01(\x08H\x12\x88\x01\x01\x12\x15\n\x08threadId\x18\x14 \x01(\tH\x13\x88\x01\x01\x12\x17\n\nsendToChat\x18\x15 \x01(\x08H\x14\x88\x01\x01\x12\x18\n\x0bisTruncated\x18\x16 \x01(\x08H\x15\x88\x01\x01\x12\x16\n\tisRemoved\x18\x17 \x01(\x08H\x16\x88\x01\x01\x12\x16\n\tchannelId\x18\x18 \x01(\tH\x17\x88\x01\x01\x12\x1b\n\x0ethreadPosition\x18\x1c \x01(\x05H\x18\x88\x01\x01\x12\x16\n\tremoverId\x18\x1d \x01(\x03H\x19\x88\x01\x01\x12\x1e\n\x11translateLanguage\x18\x1e \x01(\tH\x1a\x88\x01\x01\x12/\n\x0bremoverType\x18\x1f \x01(\x0e\x32\x15.entities.RemoverTypeH\x1b\x88\x01\x01\x12\x1a\n\rnoBadgedCount\x18! \x01(\x05H\x1c\x88\x01\x01\x12\x15\n\x08isBadged\x18\" \x01(\x08H\x1d\x88\x01\x01\x12\x17\n\nbadgeCount\x18# \x01(\x05H\x1e\x88\x01\x01\x12\x1d\n\x10threadBadgeCount\x18$ \x01(\x05H\x1f\x88\x01\x01\x12\x1d\n\x10threadReplyCount\x18% \x01(\x05H \x88\x01\x01\x12\x17\n\x0f\x61tOutChatterIds\x18& \x03(\t\x12\x1c\n\x0fmessageLanguage\x18\' \x01(\tH!\x88\x01\x01\x12\x1d\n\x10isNoTraceRemoved\x18) \x01(\x08H\"\x88\x01\x01\x12\'\n\x1aisAutoTranslatedByReceiver\x18* \x01(\x08H#\x88\x01\x01\x12\x36\n\x0bsensitivity\x18+ \x01(\x0e\x32\x1c.entities.MessageSensitivityH$\x88\x01\x01\x12\x18\n\x0bisVisibleV2\x18, \x01(\x08H%\x88\x01\x01\x12)\n\x08\x63hatType\x18. \x01(\x0e\x32\x12.entities.ChatTypeH&\x88\x01\x01\x12\x1d\n\x10originalSenderId\x18/ \x01(\tH\'\x88\x01\x01\x12+\n\x1eisStaticResourceMessageDeleted\x18\x30 \x01(\x08H(\x88\x01\x01\x12\x1f\n\x12messagePipeVersion\x18\x34 \x01(\x03H)\x88\x01\x01\x12 \n\x13isBatchCopyMessages\x18\x35 \x01(\x08H*\x88\x01\x01\x12\x1b\n\x0eisSpecialFocus\x18\x38 \x01(\x08H+\x88\x01\x01\x12\x1c\n\x0fisIncludeDocUrl\x18: \x01(\x08H,\x88\x01\x01\x12\x15\n\x08\x63ipherId\x18; \x01(\x03H-\x88\x01\x01\"\xe9\x02\n\x04Type\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x08\n\x04POST\x10\x02\x12\x08\n\x04\x46ILE\x10\x03\x12\x08\n\x04TEXT\x10\x04\x12\t\n\x05IMAGE\x10\x05\x12\n\n\x06SYSTEM\x10\x06\x12\t\n\x05\x41UDIO\x10\x07\x12\t\n\x05\x45MAIL\x10\x08\x12\x14\n\x10SHARE_GROUP_CHAT\x10\t\x12\x0b\n\x07STICKER\x10\n\x12\x11\n\rMERGE_FORWARD\x10\x0b\x12\x0c\n\x08\x43\x41LENDAR\x10\x0c\x12\x0e\n\nCLOUD_FILE\x10\r\x12\x08\n\x04\x43\x41RD\x10\x0e\x12\t\n\x05MEDIA\x10\x0f\x12\x18\n\x14SHARE_CALENDAR_EVENT\x10\x10\x12\x0b\n\x07HONGBAO\x10\x11\x12\x14\n\x10GENERAL_CALENDAR\x10\x12\x12\x0e\n\nVIDEO_CHAT\x10\x13\x12\x0c\n\x08LOCATION\x10\x14\x12\x1a\n\x16\x43OMMERCIALIZED_HONGBAO\x10\x16\x12\x13\n\x0fSHARE_USER_CARD\x10\x17\x12\x08\n\x04TODO\x10\x18\x12\n\n\x06\x46OLDER\x10\x19\x42\x05\n\x03_idB\x07\n\x05_typeB\t\n\x07_fromIdB\r\n\x0b_createTimeB\n\n\x08_contentB\t\n\x07_statusB\x0b\n\t_fromTypeB\t\n\x07_rootIdB\x0b\n\t_parentIdB\t\n\x07_chatIdB\x11\n\x0f_lastModifyTimeB\x06\n\x04_cidB\x0b\n\t_positionB\r\n\x0b_updateTimeB\r\n\x0b_isNotifiedB\r\n\x0b_replyCountB\x18\n\x16_parentSourceMessageIdB\x16\n\x14_rootSourceMessageIdB\t\n\x07_isDingB\x0b\n\t_threadIdB\r\n\x0b_sendToChatB\x0e\n\x0c_isTruncatedB\x0c\n\n_isRemovedB\x0c\n\n_channelIdB\x11\n\x0f_threadPositionB\x0c\n\n_removerIdB\x14\n\x12_translateLanguageB\x0e\n\x0c_removerTypeB\x10\n\x0e_noBadgedCountB\x0b\n\t_isBadgedB\r\n\x0b_badgeCountB\x13\n\x11_threadBadgeCountB\x13\n\x11_threadReplyCountB\x12\n\x10_messageLanguageB\x13\n\x11_isNoTraceRemovedB\x1d\n\x1b_isAutoTranslatedByReceiverB\x0e\n\x0c_sensitivityB\x0e\n\x0c_isVisibleV2B\x0b\n\t_chatTypeB\x13\n\x11_originalSenderIdB!\n\x1f_isStaticResourceMessageDeletedB\x15\n\x13_messagePipeVersionB\x16\n\x14_isBatchCopyMessagesB\x11\n\x0f_isSpecialFocusB\x12\n\x10_isIncludeDocUrlB\x0b\n\t_cipherId\"3\n\x08\x46romType\x12\x14\n\x10UNKNOWN_FROMTYPE\x10\x00\x12\x08\n\x04USER\x10\x01\x12\x07\n\x03\x42OT\x10\x02\"C\n\x06Status\x12\x12\n\x0eUNKNOWN_STATUS\x10\x00\x12\n\n\x06NORMAL\x10\x01\x12\x0b\n\x07\x44\x45LETED\x10\x02\x12\x0c\n\x08MODIFIED\x10\x03\"T\n\x0bRemoverType\x12\x17\n\x13UNKNOWN_REMOVERTYPE\x10\x00\x12\x0e\n\nGROUPOWNER\x10\x01\x12\x0c\n\x08SYSADMIN\x10\x02\x12\x0e\n\nGROUPADMIN\x10\x03\"F\n\x12MessageSensitivity\x12\x17\n\x13UNKNOWN_SENSITIVITY\x10\x00\x12\x08\n\x04SAFE\x10\x01\x12\r\n\tDANGEROUS\x10\x02\"F\n\x08\x43hatType\x12\x15\n\x11UNKNOWN_CHAT_TYPE\x10\x00\x12\x07\n\x03P2P\x10\x01\x12\t\n\x05GROUP\x10\x02\x12\x0f\n\x0bTOPIC_GROUP\x10\x03\"X\n\x0bTextContent\x12\x11\n\x04text\x18\x01 \x01(\tH\x00\x88\x01\x01\x12 \n\x08richText\x18\x03 \x01(\x0b\x32\t.RichTextH\x01\x88\x01\x01\x42\x07\n\x05_textB\x0b\n\t_richText\"\xa0\x02\n\x08RichText\x12\x12\n\nelementIds\x18\x01 \x03(\t\x12\x16\n\tinnerText\x18\x02 \x01(\tH\x00\x88\x01\x01\x12(\n\x08\x65lements\x18\x03 \x01(\x0b\x32\x11.RichTextElementsH\x01\x88\x01\x01\x12\x10\n\x08imageIds\x18\x05 \x03(\t\x12\r\n\x05\x61tIds\x18\x06 \x03(\t\x12\x11\n\tanchorIds\x18\x07 \x03(\t\x12\x0f\n\x07i18nIds\x18\x08 \x03(\t\x12\x10\n\x08mediaIds\x18\t \x03(\t\x12\x0f\n\x07\x64ocsIds\x18\n \x03(\t\x12\x16\n\x0einteractiveIds\x18\x0b \x03(\t\x12\x12\n\nmentionIds\x18\x0c \x03(\t\x12\x0f\n\x07version\x18\r \x01(\x05\x42\x0c\n\n_innerTextB\x0b\n\t_elements\"\xcf\x03\n\x10RichTextElements\x12\x35\n\ndictionary\x18\x01 \x03(\x0b\x32!.RichTextElements.DictionaryEntry\x12\x33\n\tstyleRefs\x18\x02 \x03(\x0b\x32 .RichTextElements.StyleRefsEntry\x12/\n\x06styles\x18\x03 \x03(\x0b\x32\x1f.RichTextElements.RichTextStyle\x1a\x43\n\x0f\x44ictionaryEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x1f\n\x05value\x18\x02 \x01(\x0b\x32\x10.RichTextElement:\x02\x38\x01\x1a\\\n\x0eStyleRefsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x39\n\x05value\x18\x02 \x01(\x0b\x32*.RichTextElements.RichTextElementStyleRefs:\x02\x38\x01\x1aI\n\rRichTextStyle\x12\x11\n\x04name\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x12\n\x05value\x18\x02 \x01(\tH\x01\x88\x01\x01\x42\x07\n\x05_nameB\x08\n\x06_value\x1a\x30\n\x18RichTextElementStyleRefs\x12\x14\n\x08styleIds\x18\x01 \x03(\x05\x42\x02\x10\x00\"\x9b\x05\n\x0fRichTextElement\x12&\n\x03tag\x18\x01 \x01(\x0e\x32\x14.RichTextElement.TagH\x00\x88\x01\x01\x12*\n\x05style\x18\x02 \x03(\x0b\x32\x1b.RichTextElement.StyleEntry\x12\x15\n\x08property\x18\x03 \x01(\x0cH\x01\x88\x01\x01\x12\x10\n\x08\x63hildIds\x18\x04 \x03(\t\x12\x11\n\tstyleKeys\x18\x05 \x03(\t\x1a,\n\nStyleEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xb4\x03\n\x03Tag\x12\x0f\n\x0bUNKNOWN_TAG\x10\x00\x12\x08\n\x04TEXT\x10\x01\x12\x07\n\x03IMG\x10\x02\x12\x05\n\x01P\x10\x03\x12\n\n\x06\x46IGURE\x10\x04\x12\x06\n\x02\x41T\x10\x05\x12\x05\n\x01\x41\x10\x06\x12\x05\n\x01\x42\x10\x07\x12\x05\n\x01I\x10\x08\x12\x05\n\x01U\x10\t\x12\x0b\n\x07\x45MOTION\x10\n\x12\n\n\x06\x42UTTON\x10\x0b\x12\n\n\x06SELECT\x10\x0c\x12\x1a\n\x16PROGRESS_SELECT_OPTION\x10\r\x12\x07\n\x03\x44IV\x10\x0e\x12\x11\n\rTEXTABLE_AREA\x10\x0f\x12\x08\n\x04TIME\x10\x10\x12\x08\n\x04LINK\x10\x11\x12\t\n\x05MEDIA\x10\x12\x12\x0e\n\nSELECTMENU\x10\x13\x12\x10\n\x0cOVERFLOWMENU\x10\x14\x12\x0e\n\nDATEPICKER\x10\x15\x12\x08\n\x04\x44OCS\x10\x16\x12\x06\n\x02H1\x10\x17\x12\x06\n\x02H2\x10\x18\x12\x06\n\x02H3\x10\x19\x12\x06\n\x02UL\x10\x1a\x12\x06\n\x02OL\x10\x1b\x12\x06\n\x02LI\x10\x1c\x12\t\n\x05QUOTE\x10\x1d\x12\x08\n\x04\x43ODE\x10\x1e\x12\x0e\n\nCODE_BLOCK\x10\x1f\x12\x06\n\x02HR\x10 \x12\x0e\n\nTIMEPICKER\x10!\x12\x12\n\x0e\x44\x41TETIMEPICKER\x10\"\x12\x0c\n\x08REACTION\x10#\x12\x0b\n\x07MENTION\x10$B\x06\n\x04_tagB\x0b\n\t_property\"\x80\x01\n\x0cTextProperty\x12\x14\n\x07\x63ontent\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x14\n\x07i18nKey\x18\x02 \x01(\tH\x01\x88\x01\x01\x12\x1a\n\rnumberOfLines\x18\x03 \x01(\x05H\x02\x88\x01\x01\x42\n\n\x08_contentB\n\n\x08_i18nKeyB\x10\n\x0e_numberOfLines\"G\n\rExtendedEntry\x12\x10\n\x03key\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x12\n\x05value\x18\x02 \x01(\tH\x01\x88\x01\x01\x42\x06\n\x04_keyB\x08\n\x06_value\"@\n\nPipeEntity\x12\x11\n\x04type\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x0f\n\x02id\x18\x03 \x01(\x03H\x01\x88\x01\x01\x42\x07\n\x05_typeB\x05\n\x03_id\"m\n\x0eVersionPayload\x12(\n\x0cversionRange\x18\x01 \x01(\x0b\x32\r.VersionRangeH\x00\x88\x01\x01\x12\x14\n\x07payload\x18\x02 \x01(\x0cH\x01\x88\x01\x01\x42\x0f\n\r_versionRangeB\n\n\x08_payload\"F\n\x0cVersionRange\x12\x12\n\x05start\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x10\n\x03\x65nd\x18\x02 \x01(\tH\x01\x88\x01\x01\x42\x08\n\x06_startB\x06\n\x04_end\"\xe0\x1f\n\x04\x43hat\x12\x0f\n\x02id\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x1d\n\x04type\x18\x02 \x01(\x0e\x32\n.Chat.TypeH\x01\x88\x01\x01\x12\x1a\n\rlastMessageId\x18\x03 \x01(\tH\x02\x88\x01\x01\x12\x11\n\x04name\x18\x04 \x01(\tH\x03\x88\x01\x01\x12\x14\n\x07ownerId\x18\x06 \x01(\tH\x04\x88\x01\x01\x12\x1c\n\x0fnewMessageCount\x18\x07 \x01(\x05H\x05\x88\x01\x01\x12!\n\x06status\x18\x08 \x01(\x0e\x32\x0c.Chat.StatusH\x06\x88\x01\x01\x12\x17\n\nupdateTime\x18\t \x01(\x03H\x07\x88\x01\x01\x12\x10\n\x03key\x18\n \x01(\tH\x08\x88\x01\x01\x12\x18\n\x0b\x64\x65scription\x18\x0b \x01(\tH\t\x88\x01\x01\x12\x18\n\x0bmemberCount\x18\x0c \x01(\x05H\n\x88\x01\x01\x12\x19\n\x0cisDepartment\x18\r \x01(\x08H\x0b\x88\x01\x01\x12\x15\n\x08isPublic\x18\x0e \x01(\x08H\x0c\x88\x01\x01\x12 \n\x13lastMessagePosition\x18\x0f \x01(\x05H\r\x88\x01\x01\x12\x16\n\tuserCount\x18\x10 \x01(\x05H\x0e\x88\x01\x01\x12\x17\n\nnamePinyin\x18\x11 \x01(\tH\x0f\x88\x01\x01\x12\x17\n\ncreateTime\x18\x12 \x01(\x03H\x10\x88\x01\x01\x12\x1e\n\x11isCustomerService\x18\x13 \x01(\x08H\x11\x88\x01\x01\x12\x1d\n\x04role\x18\x14 \x01(\x0e\x32\n.Chat.RoleH\x12\x88\x01\x01\x12\x19\n\x0cisCustomIcon\x18\x15 \x01(\x08H\x13\x88\x01\x01\x12$\n\x17noBadgedNewMessageCount\x18\x16 \x01(\x05H\x14\x88\x01\x01\x12!\n\x14offEditGroupChatInfo\x18\x17 \x01(\x08H\x15\x88\x01\x01\x12-\n\x0c\x61nnouncement\x18\x18 \x01(\x0b\x32\x12.Chat.AnnouncementH\x16\x88\x01\x01\x12\x15\n\x08tenantId\x18\x19 \x01(\tH\x17\x88\x01\x01\x12\x19\n\x0cupdateTimeMs\x18\x1a \x01(\x03H\x18\x88\x01\x01\x12\x15\n\x08isRemind\x18\x1b \x01(\x08H\x19\x88\x01\x01\x12\x18\n\x0bisDissolved\x18\x1e \x01(\x08H\x1a\x88\x01\x01\x12\x16\n\tisMeeting\x18\x1f \x01(\x08H\x1b\x88\x01\x01\x12!\n\x14lastVisibleMessageId\x18  \x01(\tH\x1c\x88\x01\x01\x12\x19\n\x0clastThreadId\x18! \x01(\tH\x1d\x88\x01\x01\x12\x1b\n\x0enewThreadCount\x18\" \x01(\x05H\x1e\x88\x01\x01\x12\x1f\n\x12lastThreadPosition\x18# \x01(\x05H\x1f\x88\x01\x01\x12\x15\n\x08isCrypto\x18$ \x01(\x08H \x88\x01\x01\x12#\n\x16noBadgedNewThreadCount\x18% \x01(\x05H!\x88\x01\x01\x12 \n\x13threadStartPosition\x18& \x01(\x05H\"\x88\x01\x01\x12%\n\x08\x63hatMode\x18\' \x01(\x0e\x32\x0e.Chat.ChatModeH#\x88\x01\x01\x12\x1a\n\risCrossTenant\x18) \x01(\x08H$\x88\x01\x01\x12\x15\n\x08isTenant\x18* \x01(\x08H%\x88\x01\x01\x12+\n\x0bsupportView\x18+ \x01(\x0e\x32\x11.Chat.SupportViewH&\x88\x01\x01\x12\x17\n\njoinTimeMs\x18, \x01(\x03H\'\x88\x01\x01\x12\x15\n\x08oncallId\x18- \x01(\x03H(\x88\x01\x01\x12\'\n\x1alastVisibleMessagePosition\x18. \x01(\x05H)\x88\x01\x01\x12,\n\x1flastVisibleMessageNoBadgedCount\x18/ \x01(\x05H*\x88\x01\x01\x12\x19\n\x0creadPosition\x18\x30 \x01(\x05H+\x88\x01\x01\x12#\n\x16readPositionBadgeCount\x18\x31 \x01(\x05H,\x88\x01\x01\x12*\n\x1dlastMessagePositionBadgeCount\x18\x32 \x01(\x05H-\x88\x01\x01\x12\x1c\n\x0f\x65nableWatermark\x18\x33 \x01(\x08H.\x88\x01\x01\x12\x16\n\tsidebarId\x18\x35 \x01(\tH/\x88\x01\x01\x12\x13\n\x06namePy\x18\x64 \x01(\tH0\x88\x01\x01\x12+\n\ti18nNames\x18\x65 \x03(\x0b\x32\x14.Chat.I18nNamesEntryB\x02\x18\x01\x12#\n\x07i18nInf\x18\x66 \x01(\x0b\x32\r.Chat.I18nInfH1\x88\x01\x01\x12\x1f\n\x12readThreadPosition\x18g \x01(\x05H2\x88\x01\x01\x12)\n\x1creadThreadPositionBadgeCount\x18h \x01(\x05H3\x88\x01\x01\x12)\n\x1clastThreadPositionBadgeCount\x18i \x01(\x05H4\x88\x01\x01\x12&\n\x19lastVisibleThreadPosition\x18j \x01(\x05H5\x88\x01\x01\x12 \n\x13lastVisibleThreadId\x18k \x01(\tH6\x88\x01\x01\x12\x17\n\nisPublicV2\x18m \x01(\x08H7\x88\x01\x01\x12\x16\n\tallowPost\x18o \x01(\x08H8\x88\x01\x01\x12\x17\n\nburnedTime\x18p \x01(\x03H9\x88\x01\x01\x12!\n\x14putChatterApplyCount\x18q \x01(\x05H:\x88\x01\x01\x12\x17\n\nshowBanner\x18r \x01(\x08H;\x88\x01\x01\x12\x19\n\x0cisLargeGroup\x18s \x01(\x08H<\x88\x01\x01\x12%\n\x18\x66irstChatMessagePosition\x18t \x01(\x05H=\x88\x01\x01\x12\x10\n\x04tags\x18u \x03(\x05\x42\x02\x10\x00\x12\x1f\n\x05\x65xtra\x18v \x03(\x0b\x32\x10.Chat.ExtraEntry\x12\x1e\n\x11isSamePageMeeting\x18w \x01(\x08H>\x88\x01\x01\x12#\n\x16myThreadsReadTimestamp\x18x \x01(\x03H?\x88\x01\x01\x12#\n\x16myThreadsLastTimestamp\x18y \x01(\x03H@\x88\x01\x01\x1a\x30\n\x0eI18nNamesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a,\n\nExtraEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\xd2\x01\n\x0c\x41nnouncement\x12\x14\n\x07\x63ontent\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x17\n\nupdateTime\x18\x02 \x01(\x03H\x01\x88\x01\x01\x12\x19\n\x0clastEditorId\x18\x03 \x01(\tH\x02\x88\x01\x01\x12\x13\n\x06\x64ocUrl\x18\x04 \x01(\tH\x03\x88\x01\x01\x12\x1a\n\renableOpendoc\x18\x05 \x01(\x08H\x04\x88\x01\x01\x42\n\n\x08_contentB\r\n\x0b_updateTimeB\x0f\n\r_lastEditorIdB\t\n\x07_docUrlB\x10\n\x0e_enableOpendoc\x1al\n\x07I18nInf\x12/\n\ti18nNames\x18\x01 \x03(\x0b\x32\x1c.Chat.I18nInf.I18nNamesEntry\x1a\x30\n\x0eI18nNamesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"8\n\x04Type\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x07\n\x03P2P\x10\x01\x12\t\n\x05GROUP\x10\x02\x12\x0f\n\x0bTOPIC_GROUP\x10\x03\".\n\x06Status\x12\n\n\x06NORMAL\x10\x00\x12\x0b\n\x07\x41RCHIVE\x10\x01\x12\x0b\n\x07\x44\x45LETED\x10\x02\"I\n\x08\x43hatMode\x12\x15\n\x11UNKNOWN_CHAT_MODE\x10\x00\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x01\x12\n\n\x06THREAD\x10\x02\x12\r\n\tTHREAD_V2\x10\x03\"f\n\x0bSupportView\x12\x10\n\x0cVIEW_UNKNOWN\x10\x00\x12\x11\n\rVIEW_P2PGROUP\x10\x01\x12\x10\n\x0cVIEW_MEETING\x10\x02\x12\x0f\n\x0bVIEW_THREAD\x10\x03\x12\x0f\n\x0bVIEW_CRYPTO\x10\x04\"@\n\x04Role\x12\n\n\x06IGNORE\x10\x00\x12\n\n\x06MEMBER\x10\x01\x12\x0b\n\x07VISITOR\x10\x02\x12\x13\n\x0fTHREAD_FOLLOWER\x10\x03\x42\x05\n\x03_idB\x07\n\x05_typeB\x10\n\x0e_lastMessageIdB\x07\n\x05_nameB\n\n\x08_ownerIdB\x12\n\x10_newMessageCountB\t\n\x07_statusB\r\n\x0b_updateTimeB\x06\n\x04_keyB\x0e\n\x0c_descriptionB\x0e\n\x0c_memberCountB\x0f\n\r_isDepartmentB\x0b\n\t_isPublicB\x16\n\x14_lastMessagePositionB\x0c\n\n_userCountB\r\n\x0b_namePinyinB\r\n\x0b_createTimeB\x14\n\x12_isCustomerServiceB\x07\n\x05_roleB\x0f\n\r_isCustomIconB\x1a\n\x18_noBadgedNewMessageCountB\x17\n\x15_offEditGroupChatInfoB\x0f\n\r_announcementB\x0b\n\t_tenantIdB\x0f\n\r_updateTimeMsB\x0b\n\t_isRemindB\x0e\n\x0c_isDissolvedB\x0c\n\n_isMeetingB\x17\n\x15_lastVisibleMessageIdB\x0f\n\r_lastThreadIdB\x11\n\x0f_newThreadCountB\x15\n\x13_lastThreadPositionB\x0b\n\t_isCryptoB\x19\n\x17_noBadgedNewThreadCountB\x16\n\x14_threadStartPositionB\x0b\n\t_chatModeB\x10\n\x0e_isCrossTenantB\x0b\n\t_isTenantB\x0e\n\x0c_supportViewB\r\n\x0b_joinTimeMsB\x0b\n\t_oncallIdB\x1d\n\x1b_lastVisibleMessagePositionB\"\n _lastVisibleMessageNoBadgedCountB\x0f\n\r_readPositionB\x19\n\x17_readPositionBadgeCountB \n\x1e_lastMessagePositionBadgeCountB\x12\n\x10_enableWatermarkB\x0c\n\n_sidebarIdB\t\n\x07_namePyB\n\n\x08_i18nInfB\x15\n\x13_readThreadPositionB\x1f\n\x1d_readThreadPositionBadgeCountB\x1f\n\x1d_lastThreadPositionBadgeCountB\x1c\n\x1a_lastVisibleThreadPositionB\x16\n\x14_lastVisibleThreadIdB\r\n\x0b_isPublicV2B\x0c\n\n_allowPostB\r\n\x0b_burnedTimeB\x17\n\x15_putChatterApplyCountB\r\n\x0b_showBannerB\x0f\n\r_isLargeGroupB\x1b\n\x19_firstChatMessagePositionB\x14\n\x12_isSamePageMeetingB\x19\n\x17_myThreadsReadTimestampB\x19\n\x17_myThreadsLastTimestamp\"\xa0\x04\n\x08\x46\x65\x65\x64\x43\x61rd\x12\x0f\n\x02id\x18\x01 \x01(\tH\x00\x88\x01\x01\x12!\n\x04type\x18\x02 \x01(\x0e\x32\x0e.FeedCard.TypeH\x01\x88\x01\x01\x12\x17\n\nupdateTime\x18\x03 \x01(\x03H\x02\x88\x01\x01\x12\x16\n\tisDelayed\x18\x04 \x01(\x08H\x03\x88\x01\x01\x12\x19\n\x0cparentCardId\x18\x05 \x01(\x03H\x04\x88\x01\x01\x12\x15\n\x08rankTime\x18\x06 \x01(\x03H\x05\x88\x01\x01\x12)\n\x08\x66\x65\x65\x64Type\x18\x07 \x01(\x0e\x32\x12.FeedCard.FeedTypeH\x06\x88\x01\x01\x12\x13\n\x06imprId\x18\x08 \x01(\tH\x07\x88\x01\x01\x12\x19\n\x0cupdateTimeMs\x18\t \x01(\x03H\x08\x88\x01\x01\"p\n\x04Type\x12\x10\n\x0cUNKNOWN_TYPE\x10\x00\x12\x08\n\x04\x43HAT\x10\x01\x12\x08\n\x04MAIL\x10\x02\x12\x07\n\x03\x44OC\x10\x03\x12\n\n\x06THREAD\x10\x04\x12\x07\n\x03\x42OX\x10\x05\x12\x0b\n\x07OPENAPP\x10\x06\x12\t\n\x05TOPIC\x10\x07\x12\x0c\n\x08\x41PP_CHAT\x10\x08\"<\n\x08\x46\x65\x65\x64Type\x12\x10\n\x0cTYPE_UNKNOWN\x10\x00\x12\r\n\tTYPE_NEWS\x10\x01\x12\x0f\n\x0bTYPE_SOCIAL\x10\x02\x42\x05\n\x03_idB\x07\n\x05_typeB\r\n\x0b_updateTimeB\x0c\n\n_isDelayedB\x0f\n\r_parentCardIdB\x0b\n\t_rankTimeB\x0b\n\t_feedTypeB\t\n\x07_imprIdB\x0f\n\r_updateTimeMs\"x\n\x12GetUserInfoRequest\x12\x13\n\x06\x63hatId\x18\x01 \x01(\x03H\x00\x88\x01\x01\x12\x13\n\x06userId\x18\x03 \x01(\x03H\x01\x88\x01\x01\x12\x15\n\x08userType\x18\x04 \x01(\x05H\x02\x88\x01\x01\x42\t\n\x07_chatIdB\t\n\x07_userIdB\x0b\n\t_userType\"\xe3\x01\n\x06\x44\x65tail\x12\x15\n\x08nickname\x18\x02 \x01(\x0cH\x00\x88\x01\x01\x12\x16\n\tnickname1\x18\x04 \x01(\x0cH\x01\x88\x01\x01\x12\x16\n\tnickname2\x18\x07 \x01(\x0cH\x02\x88\x01\x01\x12\x16\n\tnickname3\x18\r \x01(\x0cH\x03\x88\x01\x01\x12\x16\n\tnickname4\x18\x11 \x01(\x0cH\x04\x88\x01\x01\x12\x1d\n\x07locales\x18\x12 \x03(\x0b\x32\x0c.LocaleEntryB\x0b\n\t_nicknameB\x0c\n\n_nickname1B\x0c\n\n_nickname2B\x0c\n\n_nickname3B\x0c\n\n_nickname4\"o\n\x0bLocaleEntry\x12\x14\n\nkey_string\x18\x01 \x01(\tH\x00\x12\x16\n\x0ckey_numeric1\x18\x0c \x01(\rH\x00\x12\x16\n\x0ckey_numeric2\x18\r \x01(\rH\x00\x12\x13\n\x0btranslation\x18\x02 \x01(\tB\x05\n\x03key\")\n\x0eUserInfoDetail\x12\x17\n\x06\x64\x65tail\x18\x02 \x01(\x0b\x32\x07.Detail\"3\n\x08UserInfo\x12\'\n\x0euserInfoDetail\x18\x01 \x01(\x0b\x32\x0f.UserInfoDetail\"5\n\x13GetGroupInfoRequest\x12\x13\n\x06\x63hatId\x18\x01 \x01(\tH\x00\x88\x01\x01\x42\t\n\x07_chatId*H\n\nSearchType\x12\x14\n\x10TYPE_UNSPECIFIED\x10\x00\x12\x11\n\rSEARCH_TYPE_1\x10\x01\x12\x11\n\rSEARCH_TYPE_2\x10\x02*2\n\x0bPayloadType\x12\x10\n\x0cTYPE_UNKNOWN\x10\x00\x12\x07\n\x03PB2\x10\x01\x12\x08\n\x04JSON\x10\x02*\xe9\x02\n\x04Type\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x08\n\x04POST\x10\x02\x12\x08\n\x04\x46ILE\x10\x03\x12\x08\n\x04TEXT\x10\x04\x12\t\n\x05IMAGE\x10\x05\x12\n\n\x06SYSTEM\x10\x06\x12\t\n\x05\x41UDIO\x10\x07\x12\t\n\x05\x45MAIL\x10\x08\x12\x14\n\x10SHARE_GROUP_CHAT\x10\t\x12\x0b\n\x07STICKER\x10\n\x12\x11\n\rMERGE_FORWARD\x10\x0b\x12\x0c\n\x08\x43\x41LENDAR\x10\x0c\x12\x0e\n\nCLOUD_FILE\x10\r\x12\x08\n\x04\x43\x41RD\x10\x0e\x12\t\n\x05MEDIA\x10\x0f\x12\x18\n\x14SHARE_CALENDAR_EVENT\x10\x10\x12\x0b\n\x07HONGBAO\x10\x11\x12\x14\n\x10GENERAL_CALENDAR\x10\x12\x12\x0e\n\nVIDEO_CHAT\x10\x13\x12\x0c\n\x08LOCATION\x10\x14\x12\x1a\n\x16\x43OMMERCIALIZED_HONGBAO\x10\x16\x12\x13\n\x0fSHARE_USER_CARD\x10\x17\x12\x08\n\x04TODO\x10\x18\x12\n\n\x06\x46OLDER\x10\x19\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'proto_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_PUSHMESSAGESREQUEST_MESSAGESENTRY']._loaded_options = None
  _globals['_PUSHMESSAGESREQUEST_MESSAGESENTRY']._serialized_options = b'8\001'
  _globals['_PUSHMESSAGESREQUEST_PARTICIPATEDMESSAGEIDSENTRY']._loaded_options = None
  _globals['_PUSHMESSAGESREQUEST_PARTICIPATEDMESSAGEIDSENTRY']._serialized_options = b'8\001'
  _globals['_PUSHMESSAGESREQUEST_FORCEPUSHENTRY']._loaded_options = None
  _globals['_PUSHMESSAGESREQUEST_FORCEPUSHENTRY']._serialized_options = b'8\001'
  _globals['_PUSHMESSAGESREQUEST_MESSAGESATMEENTRY']._loaded_options = None
  _globals['_PUSHMESSAGESREQUEST_MESSAGESATMEENTRY']._serialized_options = b'8\001'
  _globals['_PUTCHATREQUEST_DOCPERMSENTRY']._loaded_options = None
  _globals['_PUTCHATREQUEST_DOCPERMSENTRY']._serialized_options = b'8\001'
  _globals['_PUTCHATREQUEST_DOCPERMS2ENTRY']._loaded_options = None
  _globals['_PUTCHATREQUEST_DOCPERMS2ENTRY']._serialized_options = b'8\001'
  _globals['_PUTCHATREQUEST_DOCPAIR_PERMSENTRY']._loaded_options = None
  _globals['_PUTCHATREQUEST_DOCPAIR_PERMSENTRY']._serialized_options = b'8\001'
  _globals['_USERFILTER_CUSTOMFIELDSENTRY']._loaded_options = None
  _globals['_USERFILTER_CUSTOMFIELDSENTRY']._serialized_options = b'8\001'
  _globals['_RICHTEXTELEMENTS_DICTIONARYENTRY']._loaded_options = None
  _globals['_RICHTEXTELEMENTS_DICTIONARYENTRY']._serialized_options = b'8\001'
  _globals['_RICHTEXTELEMENTS_STYLEREFSENTRY']._loaded_options = None
  _globals['_RICHTEXTELEMENTS_STYLEREFSENTRY']._serialized_options = b'8\001'
  _globals['_RICHTEXTELEMENTS_RICHTEXTELEMENTSTYLEREFS'].fields_by_name['styleIds']._loaded_options = None
  _globals['_RICHTEXTELEMENTS_RICHTEXTELEMENTSTYLEREFS'].fields_by_name['styleIds']._serialized_options = b'\020\000'
  _globals['_RICHTEXTELEMENT_STYLEENTRY']._loaded_options = None
  _globals['_RICHTEXTELEMENT_STYLEENTRY']._serialized_options = b'8\001'
  _globals['_CHAT_I18NNAMESENTRY']._loaded_options = None
  _globals['_CHAT_I18NNAMESENTRY']._serialized_options = b'8\001'
  _globals['_CHAT_EXTRAENTRY']._loaded_options = None
  _globals['_CHAT_EXTRAENTRY']._serialized_options = b'8\001'
  _globals['_CHAT_I18NINF_I18NNAMESENTRY']._loaded_options = None
  _globals['_CHAT_I18NINF_I18NNAMESENTRY']._serialized_options = b'8\001'
  _globals['_CHAT'].fields_by_name['i18nNames']._loaded_options = None
  _globals['_CHAT'].fields_by_name['i18nNames']._serialized_options = b'\030\001'
  _globals['_CHAT'].fields_by_name['tags']._loaded_options = None
  _globals['_CHAT'].fields_by_name['tags']._serialized_options = b'\020\000'
  _globals['_SEARCHTYPE']._serialized_start=19169
  _globals['_SEARCHTYPE']._serialized_end=19241
  _globals['_PAYLOADTYPE']._serialized_start=19243
  _globals['_PAYLOADTYPE']._serialized_end=19293
  _globals['_TYPE']._serialized_start=10492
  _globals['_TYPE']._serialized_end=10853
  _globals['_FRAME']._serialized_start=16
  _globals['_FRAME']._serialized_end=308
  _globals['_PACKET']._serialized_start=311
  _globals['_PACKET']._serialized_end=769
  _globals['_PUSHMESSAGESREQUEST']._serialized_start=772
  _globals['_PUSHMESSAGESREQUEST']._serialized_end=1281
  _globals['_PUSHMESSAGESREQUEST_MESSAGESENTRY']._serialized_start=1049
  _globals['_PUSHMESSAGESREQUEST_MESSAGESENTRY']._serialized_end=1115
  _globals['_PUSHMESSAGESREQUEST_PARTICIPATEDMESSAGEIDSENTRY']._serialized_start=1117
  _globals['_PUSHMESSAGESREQUEST_PARTICIPATEDMESSAGEIDSENTRY']._serialized_end=1178
  _globals['_PUSHMESSAGESREQUEST_FORCEPUSHENTRY']._serialized_start=1180
  _globals['_PUSHMESSAGESREQUEST_FORCEPUSHENTRY']._serialized_end=1228
  _globals['_PUSHMESSAGESREQUEST_MESSAGESATMEENTRY']._serialized_start=1230
  _globals['_PUSHMESSAGESREQUEST_MESSAGESATMEENTRY']._serialized_end=1281
  _globals['_PUTMESSAGEREQUEST']._serialized_start=1284
  _globals['_PUTMESSAGEREQUEST']._serialized_end=1798
  _globals['_PUTCHATREQUEST']._serialized_start=1801
  _globals['_PUTCHATREQUEST']._serialized_end=2698
  _globals['_PUTCHATREQUEST_DOCPERMSENTRY']._serialized_start=2224
  _globals['_PUTCHATREQUEST_DOCPERMSENTRY']._serialized_end=2300
  _globals['_PUTCHATREQUEST_DOCPERMS2ENTRY']._serialized_start=2302
  _globals['_PUTCHATREQUEST_DOCPERMS2ENTRY']._serialized_end=2375
  _globals['_PUTCHATREQUEST_DOCPAIR']._serialized_start=2378
  _globals['_PUTCHATREQUEST_DOCPAIR']._serialized_end=2513
  _globals['_PUTCHATREQUEST_DOCPAIR_PERMSENTRY']._serialized_start=2440
  _globals['_PUTCHATREQUEST_DOCPAIR_PERMSENTRY']._serialized_end=2513
  _globals['_PUTCHATREQUEST_DOCPERMTYPE']._serialized_start=2515
  _globals['_PUTCHATREQUEST_DOCPERMTYPE']._serialized_end=2561
  _globals['_PUTCHATRESPONSE']._serialized_start=2700
  _globals['_PUTCHATRESPONSE']._serialized_end=2799
  _globals['_UNIVERSALSEARCHREQUEST']._serialized_start=2801
  _globals['_UNIVERSALSEARCHREQUEST']._serialized_end=2885
  _globals['_UNIVERSALSEARCHRESPONSE']._serialized_start=2888
  _globals['_UNIVERSALSEARCHRESPONSE']._serialized_end=5748
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHRESULT']._serialized_start=3186
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHRESULT']._serialized_end=3551
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHCOMMONRESPONSEHEADER']._serialized_start=3554
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHCOMMONRESPONSEHEADER']._serialized_end=4721
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHCOMMONRESPONSEHEADER_COLDANDHOTSTORAGEINFO']._serialized_start=3922
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHCOMMONRESPONSEHEADER_COLDANDHOTSTORAGEINFO']._serialized_end=4491
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHCOMMONRESPONSEHEADER_COLDANDHOTSTORAGEINFO_TIMEUNIT']._serialized_start=4242
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHCOMMONRESPONSEHEADER_COLDANDHOTSTORAGEINFO_TIMEUNIT']._serialized_end=4309
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHCOMMONRESPONSEHEADER_COLDANDHOTSTORAGEINFO_HASMOREINFO']._serialized_start=4311
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHCOMMONRESPONSEHEADER_COLDANDHOTSTORAGEINFO_HASMOREINFO']._serialized_end=4417
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHCOMMONRESPONSEHEADER_INVOKEABNORMALNOTICE']._serialized_start=4493
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHCOMMONRESPONSEHEADER_INVOKEABNORMALNOTICE']._serialized_end=4603
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHEXTRAFIELDS']._serialized_start=4723
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHEXTRAFIELDS']._serialized_end=4812
  _globals['_UNIVERSALSEARCHRESPONSE_FAILEDENTITYINFO']._serialized_start=4815
  _globals['_UNIVERSALSEARCHRESPONSE_FAILEDENTITYINFO']._serialized_end=5386
  _globals['_UNIVERSALSEARCHRESPONSE_FAILEDENTITYINFO_SEARCHENTITYTYPE']._serialized_start=4990
  _globals['_UNIVERSALSEARCHRESPONSE_FAILEDENTITYINFO_SEARCHENTITYTYPE']._serialized_end=5322
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHENTITYTYPE']._serialized_start=4990
  _globals['_UNIVERSALSEARCHRESPONSE_SEARCHENTITYTYPE']._serialized_end=5322
  _globals['_SEARCHCOMMONREQUESTHEADER']._serialized_start=5751
  _globals['_SEARCHCOMMONREQUESTHEADER']._serialized_end=6513
  _globals['_SEARCHCOMMONREQUESTHEADER_LAYOUT']._serialized_start=6249
  _globals['_SEARCHCOMMONREQUESTHEADER_LAYOUT']._serialized_end=6315
  _globals['_SEARCHEXTRAPARAM']._serialized_start=6516
  _globals['_SEARCHEXTRAPARAM']._serialized_end=6652
  _globals['_BASEENTITY']._serialized_start=6655
  _globals['_BASEENTITY']._serialized_end=6850
  _globals['_BASEENTITY_SEARCHCONTEXT']._serialized_start=6670
  _globals['_BASEENTITY_SEARCHCONTEXT']._serialized_end=6850
  _globals['_COMMONFILTER']._serialized_start=6852
  _globals['_COMMONFILTER']._serialized_end=6954
  _globals['_USERFILTER']._serialized_start=6957
  _globals['_USERFILTER']._serialized_end=7247
  _globals['_USERFILTER_FIELDVALUES']._serialized_start=7097
  _globals['_USERFILTER_FIELDVALUES']._serialized_end=7126
  _globals['_USERFILTER_CUSTOMFIELDSENTRY']._serialized_start=7128
  _globals['_USERFILTER_CUSTOMFIELDSENTRY']._serialized_end=7204
  _globals['_GROUPCHATFILTER']._serialized_start=7250
  _globals['_GROUPCHATFILTER']._serialized_end=7439
  _globals['_ENTITYITEM']._serialized_start=7442
  _globals['_ENTITYITEM']._serialized_end=8093
  _globals['_ENTITYITEM_ENTITYFILTER']._serialized_start=7609
  _globals['_ENTITYITEM_ENTITYFILTER']._serialized_end=7713
  _globals['_ENTITYITEM_SEARCHENTITYTYPE']._serialized_start=4990
  _globals['_ENTITYITEM_SEARCHENTITYTYPE']._serialized_end=5322
  _globals['_CONTENT']._serialized_start=8096
  _globals['_CONTENT']._serialized_end=9082
  _globals['_CONTENT_FILETRANSMODE']._serialized_start=8711
  _globals['_CONTENT_FILETRANSMODE']._serialized_end=8754
  _globals['_ENTITIES']._serialized_start=9085
  _globals['_ENTITIES']._serialized_end=11967
  _globals['_ENTITIES_MESSAGE']._serialized_start=9098
  _globals['_ENTITIES_MESSAGE']._serialized_end=11615
  _globals['_ENTITIES_MESSAGE_TYPE']._serialized_start=10492
  _globals['_ENTITIES_MESSAGE_TYPE']._serialized_end=10853
  _globals['_ENTITIES_FROMTYPE']._serialized_start=11617
  _globals['_ENTITIES_FROMTYPE']._serialized_end=11668
  _globals['_ENTITIES_STATUS']._serialized_start=11670
  _globals['_ENTITIES_STATUS']._serialized_end=11737
  _globals['_ENTITIES_REMOVERTYPE']._serialized_start=11739
  _globals['_ENTITIES_REMOVERTYPE']._serialized_end=11823
  _globals['_ENTITIES_MESSAGESENSITIVITY']._serialized_start=11825
  _globals['_ENTITIES_MESSAGESENSITIVITY']._serialized_end=11895
  _globals['_ENTITIES_CHATTYPE']._serialized_start=11897
  _globals['_ENTITIES_CHATTYPE']._serialized_end=11967
  _globals['_TEXTCONTENT']._serialized_start=11969
  _globals['_TEXTCONTENT']._serialized_end=12057
  _globals['_RICHTEXT']._serialized_start=12060
  _globals['_RICHTEXT']._serialized_end=12348
  _globals['_RICHTEXTELEMENTS']._serialized_start=12351
  _globals['_RICHTEXTELEMENTS']._serialized_end=12814
  _globals['_RICHTEXTELEMENTS_DICTIONARYENTRY']._serialized_start=12528
  _globals['_RICHTEXTELEMENTS_DICTIONARYENTRY']._serialized_end=12595
  _globals['_RICHTEXTELEMENTS_STYLEREFSENTRY']._serialized_start=12597
  _globals['_RICHTEXTELEMENTS_STYLEREFSENTRY']._serialized_end=12689
  _globals['_RICHTEXTELEMENTS_RICHTEXTSTYLE']._serialized_start=12691
  _globals['_RICHTEXTELEMENTS_RICHTEXTSTYLE']._serialized_end=12764
  _globals['_RICHTEXTELEMENTS_RICHTEXTELEMENTSTYLEREFS']._serialized_start=12766
  _globals['_RICHTEXTELEMENTS_RICHTEXTELEMENTSTYLEREFS']._serialized_end=12814
  _globals['_RICHTEXTELEMENT']._serialized_start=12817
  _globals['_RICHTEXTELEMENT']._serialized_end=13484
  _globals['_RICHTEXTELEMENT_STYLEENTRY']._serialized_start=12980
  _globals['_RICHTEXTELEMENT_STYLEENTRY']._serialized_end=13024
  _globals['_RICHTEXTELEMENT_TAG']._serialized_start=13027
  _globals['_RICHTEXTELEMENT_TAG']._serialized_end=13463
  _globals['_TEXTPROPERTY']._serialized_start=13487
  _globals['_TEXTPROPERTY']._serialized_end=13615
  _globals['_EXTENDEDENTRY']._serialized_start=13617
  _globals['_EXTENDEDENTRY']._serialized_end=13688
  _globals['_PIPEENTITY']._serialized_start=13690
  _globals['_PIPEENTITY']._serialized_end=13754
  _globals['_VERSIONPAYLOAD']._serialized_start=13756
  _globals['_VERSIONPAYLOAD']._serialized_end=13865
  _globals['_VERSIONRANGE']._serialized_start=13867
  _globals['_VERSIONRANGE']._serialized_end=13937
  _globals['_CHAT']._serialized_start=13940
  _globals['_CHAT']._serialized_end=18004
  _globals['_CHAT_I18NNAMESENTRY']._serialized_start=16019
  _globals['_CHAT_I18NNAMESENTRY']._serialized_end=16067
  _globals['_CHAT_EXTRAENTRY']._serialized_start=16069
  _globals['_CHAT_EXTRAENTRY']._serialized_end=16113
  _globals['_CHAT_ANNOUNCEMENT']._serialized_start=16116
  _globals['_CHAT_ANNOUNCEMENT']._serialized_end=16326
  _globals['_CHAT_I18NINF']._serialized_start=16328
  _globals['_CHAT_I18NINF']._serialized_end=16436
  _globals['_CHAT_I18NINF_I18NNAMESENTRY']._serialized_start=16019
  _globals['_CHAT_I18NINF_I18NNAMESENTRY']._serialized_end=16067
  _globals['_CHAT_TYPE']._serialized_start=16438
  _globals['_CHAT_TYPE']._serialized_end=16494
  _globals['_CHAT_STATUS']._serialized_start=16496
  _globals['_CHAT_STATUS']._serialized_end=16542
  _globals['_CHAT_CHATMODE']._serialized_start=16544
  _globals['_CHAT_CHATMODE']._serialized_end=16617
  _globals['_CHAT_SUPPORTVIEW']._serialized_start=16619
  _globals['_CHAT_SUPPORTVIEW']._serialized_end=16721
  _globals['_CHAT_ROLE']._serialized_start=16723
  _globals['_CHAT_ROLE']._serialized_end=16787
  _globals['_FEEDCARD']._serialized_start=18007
  _globals['_FEEDCARD']._serialized_end=18551
  _globals['_FEEDCARD_TYPE']._serialized_start=18261
  _globals['_FEEDCARD_TYPE']._serialized_end=18373
  _globals['_FEEDCARD_FEEDTYPE']._serialized_start=18375
  _globals['_FEEDCARD_FEEDTYPE']._serialized_end=18435
  _globals['_GETUSERINFOREQUEST']._serialized_start=18553
  _globals['_GETUSERINFOREQUEST']._serialized_end=18673
  _globals['_DETAIL']._serialized_start=18676
  _globals['_DETAIL']._serialized_end=18903
  _globals['_LOCALEENTRY']._serialized_start=18905
  _globals['_LOCALEENTRY']._serialized_end=19016
  _globals['_USERINFODETAIL']._serialized_start=19018
  _globals['_USERINFODETAIL']._serialized_end=19059
  _globals['_USERINFO']._serialized_start=19061
  _globals['_USERINFO']._serialized_end=19112
  _globals['_GETGROUPINFOREQUEST']._serialized_start=19114
  _globals['_GETGROUPINFOREQUEST']._serialized_end=19167
# @@protoc_insertion_point(module_scope)
