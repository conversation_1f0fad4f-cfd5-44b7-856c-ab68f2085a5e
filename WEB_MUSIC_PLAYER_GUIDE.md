# LarkAgentX Web端音乐播放系统

## 🎵 概述

LarkAgentX 现已升级为Web端音乐播放模式，解决了云部署时用户无法听到音乐提醒的问题。

## 🚀 新特性

### Web端音乐播放
- ✅ **云部署友好** - 音乐在用户浏览器中播放
- ✅ **实时推送** - 服务器实时推送播放指令到Web端
- ✅ **自动循环** - 支持音乐循环播放
- ✅ **自动停止** - 30秒后自动停止，避免无限播放
- ✅ **多格式支持** - 支持MP3、WAV、OGG、M4A、AAC格式

### 移除的功能
- ❌ **pygame依赖** - 不再需要pygame库
- ❌ **本地弹窗** - 移除了桌面弹窗提醒（云部署无法显示）

## 🔧 技术架构

### 播放流程
1. **消息触发** → 消息服务检测到匹配消息
2. **事件推送** → 音乐播放器推送播放事件到状态管理器
3. **状态同步** → Web界面定期获取状态更新（每3秒）
4. **音频播放** → 浏览器接收事件并播放音频文件
5. **自动停止** → 30秒后或手动停止

### 核心组件
- **MusicPlayer** - Web端播放模式，推送播放事件
- **StateManager** - 管理音乐播放事件状态
- **WebServer** - 提供音频文件HTTP服务
- **Web界面** - HTML5 Audio + JavaScript控制

## 📁 文件修改

### 修改的文件
1. `app/utils/music_player.py` - 改为Web播放模式
2. `app/web/web_server.py` - 添加音频文件服务API
3. `app/web/state_manager.py` - 添加音乐播放事件管理
4. `app/web/templates/index.html` - 添加HTML5音频播放组件
5. `app/utils/notification.py` - 适配Web播放模式

### 新增的API
- `GET /api/audio/<filename>` - 音频文件服务接口

## 🎯 使用方法

### 1. 准备音频文件
```bash
# 将音频文件放到 static/audio/ 目录
cp your_notification.mp3 static/audio/notification.mp3
```

### 2. 配置音频文件路径
在 `.env` 文件中配置：
```env
NOTIFICATION_MUSIC_FILE=static/audio/notification.mp3
```

### 3. 启动应用
```bash
python launcher.py
```

### 4. 打开Web界面
访问：`http://127.0.0.1:8080`

### 5. 测试音乐播放
- 启动主应用
- 发送匹配的飞书消息
- 浏览器会自动播放音乐并显示提示

## 🔍 播放控制

### 自动播放
- 检测到匹配消息时自动开始播放
- 音乐循环播放
- 30秒后自动停止

### 手动控制
- 可以通过关闭音乐提醒开关来禁用
- 浏览器控制台可以看到播放日志

## 🛠️ 故障排除

### 音乐不播放
1. **检查音频文件** - 确保文件存在且格式正确
2. **检查浏览器** - 确保浏览器支持HTML5 Audio
3. **检查控制台** - 查看浏览器控制台的错误信息
4. **检查权限** - 某些浏览器需要用户交互后才能播放音频

### 浏览器兼容性
- **Chrome/Edge** - 完全支持
- **Firefox** - 完全支持  
- **Safari** - 支持，可能需要用户交互
- **移动浏览器** - 支持，但可能有限制

### 音频格式支持
- **MP3** - 所有现代浏览器支持
- **WAV** - 所有现代浏览器支持
- **OGG** - Firefox、Chrome支持
- **M4A/AAC** - Safari、Chrome支持

## 📊 性能优化

### 音频文件建议
- **时长** - 10-30秒（会循环播放）
- **大小** - 5MB以内
- **格式** - 推荐MP3（兼容性最好）
- **音质** - 128kbps足够

### 网络优化
- 音频文件会被浏览器缓存
- 支持HTTP范围请求
- 小文件加载速度快

## 🔒 安全考虑

### 路径安全
- 防止路径遍历攻击
- 只允许访问指定目录的音频文件
- 文件扩展名白名单验证

### 文件类型限制
- 只允许音频文件格式
- 文件大小限制
- 安全的文件名检查

## 🎉 优势总结

1. **云部署友好** - 完美解决云服务器音乐播放问题
2. **用户体验一致** - 无论本地还是云端都能听到音乐
3. **技术先进** - 使用现代Web技术，兼容性好
4. **维护简单** - 减少了pygame等外部依赖
5. **扩展性强** - 可以轻松添加更多音频控制功能

现在您的LarkAgentX可以在云服务器上完美运行音乐提醒功能了！🎵
