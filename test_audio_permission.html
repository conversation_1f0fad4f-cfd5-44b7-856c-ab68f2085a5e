<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频权限测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎵 音频权限测试</h1>
        <p>测试修改后的音频权限设置是否正常工作</p>
        
        <div id="status-display" class="status warning">
            ⏳ 正在初始化...
        </div>
        
        <div>
            <button onclick="testAudioPermission()">测试音频权限</button>
            <button onclick="testAudioPlayback()">测试音频播放</button>
            <button onclick="stopAudio()">停止播放</button>
        </div>
        
        <div style="margin-top: 20px;">
            <h3>测试结果：</h3>
            <ul id="test-results"></ul>
        </div>
        
        <audio id="test-audio" loop>
            <source src="static/audio/zfb100w.mp3" type="audio/mpeg">
            您的浏览器不支持音频播放
        </audio>
    </div>

    <script>
        // 模拟修改后的权限设置
        let audioPermissionGranted = true; // 默认启用音频权限
        let currentAudio = null;
        
        function addTestResult(message, type = 'info') {
            const results = document.getElementById('test-results');
            const li = document.createElement('li');
            li.innerHTML = `<span style="color: ${type === 'success' ? 'green' : type === 'error' ? 'red' : 'blue'}">${message}</span>`;
            results.appendChild(li);
        }
        
        function updateStatus(message, type = 'warning') {
            const status = document.getElementById('status-display');
            status.className = `status ${type}`;
            status.innerHTML = message;
        }
        
        function testAudioPermission() {
            addTestResult('开始测试音频权限...', 'info');
            
            // 模拟简化后的权限检查
            console.log('音频权限默认启用');
            audioPermissionGranted = true;
            
            if (audioPermissionGranted) {
                updateStatus('✅ 音频权限已启用（默认开启）', 'success');
                addTestResult('✅ 音频权限测试通过 - 默认启用', 'success');
            } else {
                updateStatus('❌ 音频权限未启用', 'error');
                addTestResult('❌ 音频权限测试失败', 'error');
            }
        }
        
        function testAudioPlayback() {
            addTestResult('开始测试音频播放...', 'info');
            
            const audio = document.getElementById('test-audio');
            
            try {
                // 停止当前播放
                if (currentAudio) {
                    currentAudio.pause();
                    currentAudio.currentTime = 0;
                }
                
                // 设置循环播放
                audio.loop = true;
                
                // 尝试播放
                const playPromise = audio.play();
                
                if (playPromise !== undefined) {
                    playPromise.then(() => {
                        console.log('音频播放成功');
                        currentAudio = audio;
                        updateStatus('🎵 音乐正在播放 - 点击停止按钮可停止', 'success');
                        addTestResult('✅ 音频播放测试通过 - 音乐正在循环播放', 'success');
                    }).catch(error => {
                        console.error('音频播放失败:', error);
                        updateStatus('❌ 音频播放失败: ' + error.message, 'error');
                        addTestResult('❌ 音频播放测试失败: ' + error.message, 'error');
                    });
                }
                
            } catch (error) {
                console.error('音频播放设置失败:', error);
                updateStatus('❌ 音频设置失败: ' + error.message, 'error');
                addTestResult('❌ 音频设置失败: ' + error.message, 'error');
            }
        }
        
        function stopAudio() {
            try {
                if (currentAudio) {
                    currentAudio.loop = false;
                    currentAudio.pause();
                    currentAudio.currentTime = 0;
                    currentAudio = null;
                    updateStatus('🔇 音乐已停止', 'warning');
                    addTestResult('✅ 音乐停止成功', 'success');
                } else {
                    addTestResult('ℹ️ 没有正在播放的音乐', 'info');
                }
            } catch (error) {
                console.error('停止音频失败:', error);
                addTestResult('❌ 停止音频失败: ' + error.message, 'error');
            }
        }
        
        // 页面加载时自动测试权限
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('✅ 页面加载完成 - 音频权限默认启用', 'success');
            addTestResult('📄 页面加载完成', 'info');
            addTestResult('🔧 音频权限默认设置为: ' + audioPermissionGranted, 'info');
            
            setTimeout(() => {
                testAudioPermission();
            }, 500);
        });
    </script>
</body>
</html>
