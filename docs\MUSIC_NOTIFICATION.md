# 音乐提醒功能使用说明

## 功能概述

当检测到匹配的飞书消息时，系统会：
1. 🎵 循环播放提醒音乐
2. 🔔 显示弹窗提醒
3. ⏹️ 用户点击"确定"后停止音乐

## 安装依赖

```bash
pip install pygame==2.5.2
```

## 配置说明

### 1. 环境变量配置

在 `.env` 文件中添加以下配置：

```env
# 启用音乐提醒功能
ENABLE_MUSIC_NOTIFICATION=true

# 音乐文件路径
NOTIFICATION_MUSIC_FILE="static/audio/notification.mp3"

# 弹窗标题
NOTIFICATION_TITLE="飞书消息提醒"

# 弹窗消息内容
NOTIFICATION_MESSAGE="您有新的飞书消息！\n点击确定停止音乐提醒"
```

### 2. 音乐文件准备

1. 在 `static/audio/` 目录下放置音乐文件
2. 支持的格式：MP3、WAV、OGG
3. 建议文件名：
   - `notification.mp3` (默认)
   - `notification.wav`
   - `alert.mp3`
   - `alert.wav`

### 3. 音乐文件要求

- **时长**：建议 10-30 秒（会循环播放）
- **音量**：适中，避免过于刺耳
- **大小**：建议 5MB 以内
- **格式**：MP3、WAV、OGG

## 使用流程

1. **触发条件**：收到匹配 `TRIGGER_PATTERN` 的消息
2. **音乐播放**：自动开始循环播放提醒音乐
3. **弹窗显示**：同时显示提醒弹窗
4. **停止音乐**：用户点击弹窗"确定"按钮
5. **发送回复**：向飞书发送回复消息

## 功能特点

- ✅ 支持多种音频格式
- ✅ 循环播放直到用户确认
- ✅ 弹窗置顶显示
- ✅ 可通过配置启用/禁用
- ✅ 自动寻找备用音乐文件
- ✅ 完整的错误处理

## 故障排除

### 1. 音乐不播放

**可能原因**：
- pygame 未安装
- 音乐文件不存在
- 音频设备问题

**解决方案**：
```bash
# 安装 pygame
pip install pygame

# 检查音乐文件是否存在
ls static/audio/

# 检查日志输出
```

### 2. 弹窗不显示

**可能原因**：
- tkinter 不可用
- 系统权限问题

**解决方案**：
- 确保运行环境支持 GUI
- 检查系统权限设置

### 3. 禁用音乐提醒

在 `.env` 文件中设置：
```env
ENABLE_MUSIC_NOTIFICATION=false
```

## 日志信息

系统会输出以下日志：
- `音乐播放器初始化成功`
- `开始播放音乐: {文件路径}`
- `音乐提醒已启动`
- `用户点击了确定按钮`
- `音乐播放已停止`

## 注意事项

1. **音乐文件**：请确保有合适的提醒音乐文件
2. **系统兼容性**：需要支持音频播放和GUI的环境
3. **权限要求**：可能需要音频设备访问权限
4. **性能影响**：音乐播放在后台线程运行，不影响主程序
