import os
from loguru import logger
from dotenv import load_dotenv

class Settings:
    """Application settings with hot reload support"""

    def __init__(self):
        self.reload()

    def reload(self):
        """重新加载配置"""
        try:
            # 重新加载.env文件，覆盖现有环境变量
            load_dotenv(override=True)

            # 飞书 API 配置
            self.LARK_COOKIE = os.getenv("LARK_COOKIE", "")
            self.LARK_BASE_URL = "https://internal-api-lark-api.feishu.cn/im/gateway/"
            self.LARK_CSRF_TOKEN_URL = "https://internal-api-lark-api.feishu.cn/accounts/csrf"
            self.LARK_USER_INFO_URL = "https://internal-api-lark-api.feishu.cn/accounts/web/user"
            self.LARK_WS_URL = "wss://msg-frontier.feishu.cn/ws/v2"

            # 消息匹配和回复配置
            self.MESSAGE_MATCH_MODE = os.getenv("MESSAGE_MATCH_MODE", "content")  # content 或 group_name

            # 内容匹配模式配置
            self.TRIGGER_PATTERN = os.getenv("TRIGGER_PATTERN", "已接通人工.*?@.*?为你服务.*?请问.*?帮你")
            self.REPLY_MESSAGE = os.getenv("REPLY_MESSAGE", "您好！有什么可以帮您？")

            # 群聊名称匹配模式配置
            self.GROUP_NAME_PATTERN = os.getenv("GROUP_NAME_PATTERN", ".*'s 门店IT服务台")
            self.GROUP_NAME_CONTENT_PATTERN = os.getenv("GROUP_NAME_CONTENT_PATTERN", "^None$")
            self.GROUP_NAME_REPLY_MESSAGE = os.getenv("GROUP_NAME_REPLY_MESSAGE", "您好！我是IT服务台智能助手，有什么可以帮您？")

            # 音乐提醒配置
            self.ENABLE_MUSIC_NOTIFICATION = os.getenv("ENABLE_MUSIC_NOTIFICATION", "true").lower() == "true"
            self.NOTIFICATION_MUSIC_FILE = os.getenv("NOTIFICATION_MUSIC_FILE", "static/audio/notification.mp3")
            self.NOTIFICATION_TITLE = os.getenv("NOTIFICATION_TITLE", "飞书消息提醒")
            self.NOTIFICATION_MESSAGE = os.getenv("NOTIFICATION_MESSAGE", "您有新的飞书消息！\n点击确定停止音乐提醒")

            # 电话提醒配置
            self.ENABLE_PHONE_NOTIFICATION = os.getenv("ENABLE_PHONE_NOTIFICATION", "true").lower() == "true"
            self.LARK_APP_ID = os.getenv("LARK_APP_ID", "")
            self.LARK_APP_SECRET = os.getenv("LARK_APP_SECRET", "")
            self.PHONE_NOTIFICATION_USER_IDS = os.getenv("PHONE_NOTIFICATION_USER_IDS", "").split(",") if os.getenv("PHONE_NOTIFICATION_USER_IDS") else []
            self.PHONE_NOTIFICATION_MESSAGE_IDS = os.getenv("PHONE_NOTIFICATION_MESSAGE_IDS", "").split(",") if os.getenv("PHONE_NOTIFICATION_MESSAGE_IDS") else []
            self.PHONE_NOTIFICATION_INTERVAL = int(os.getenv("PHONE_NOTIFICATION_INTERVAL", "15"))  # 间隔秒数

            logger.info("配置重新加载成功")
            return True
        except Exception as e:
            logger.error(f"配置重新加载失败: {str(e)}")
            return False

    def get_current_music_file(self):
        """获取当前音乐文件路径，支持实时更新"""
        # 重新读取环境变量
        current_file = os.getenv("NOTIFICATION_MUSIC_FILE", "static/audio/notification.mp3")
        return current_file

    def get_raw_cookie(self):
        """获取原始Cookie值（仅内部使用）"""
        return self.LARK_COOKIE


settings = Settings()
