# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Unit test / coverage reports
.cache
*.cover
.pytest_cache/

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Logs
*.log
logs/

# Database
local_settings.py
db.sqlite3

# Node modules
*node_modules/*

# Project specific
# 保留配置模板
!.env.example

# 音频文件（除了示例）
static/audio/*.mp3
static/audio/*.wav
static/audio/*.ogg
!static/audio/README.md

# 打包输出
LarkFlow.exe

# 临时和测试文件
test.py
test_*.py
*_test.py
*.tmp
*.temp

# IDE 文件
.vscode/
.idea/
*.swp
*.swo

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# 备份文件
*.bak
*.backup