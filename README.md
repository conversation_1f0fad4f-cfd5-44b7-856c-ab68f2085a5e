# 🚀 LarkFlow

> 基于飞书(Lark)的智能消息处理系统，支持自动回复和音乐提醒功能

[![Python](https://img.shields.io/badge/Python-3.10+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/Platform-Windows-lightgrey.svg)](https://github.com)

## ✨ 功能特性

- 🔄 **实时消息监听**: 基于 WebSocket 的实时消息接收
- 🤖 **智能自动回复**: 支持正则表达式模式匹配
- 🎵 **音乐提醒功能**: 检测到消息时播放音乐并弹窗提醒
- 📞 **电话提醒功能**: 并行发送飞书紧急电话提醒
- 🌐 **Web管理界面**: 浏览器端配置管理和状态监控
- 🔒 **安全保护**: 敏感信息脱敏显示，保护Cookie和密钥安全
- 🔐 **Cookie 认证**: 无需机器人配置，直接使用个人账号
- ⚙️ **灵活配置**: 支持环境变量配置，易于定制
- 📦 **一键打包**: 支持打包为 Windows 可执行文件

## 🏗️ 技术架构

- **后端**: Python 3.10+ + asyncio
- **通信**: WebSocket + HTTP API
- **协议**: Protocol Buffers
- **Web界面**: Flask + Bootstrap 5
- **音频**: pygame
- **GUI**: tkinter
- **打包**: PyInstaller

## 🚀 快速开始

### 方法一：Python 环境运行

1. **克隆项目**
```bash
git clone https://github.com/ljcyt/LarkFlow.git
cd LarkFlow
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境**
```bash
cp .env.example .env
# 编辑 .env 文件，填入飞书 Cookie
```

4. **运行程序**
```bash
python main.py
```

5. **访问Web管理界面**
```
http://127.0.0.1:8080
```
通过浏览器访问Web界面进行配置管理和状态监控

### 方法二：Windows 可执行文件

1. **一键打包**
```bash
# 双击运行
build.bat
```

2. **使用程序**
```bash
# 进入 dist 目录
cd dist
# 配置 .env 文件
# 双击 LarkFlow.exe
```

## ⚙️ 配置说明

### 必需配置

```env
# 飞书 Cookie（必须）
LARK_COOKIE="your_lark_cookie_here"
```

### 可选配置

```env
# 消息匹配模式
TRIGGER_PATTERN="已接通人工.*?@.*?为你服务.*?请问.*?帮你"

# 自动回复内容
REPLY_MESSAGE="您好！有什么可以帮您？"

# 音乐提醒功能
ENABLE_MUSIC_NOTIFICATION=true
NOTIFICATION_MUSIC_FILE="static/audio/notification.mp3"
NOTIFICATION_TITLE="飞书消息提醒"
NOTIFICATION_MESSAGE="您有新的飞书消息！\n点击确定停止音乐提醒"

# 电话提醒功能
ENABLE_PHONE_NOTIFICATION=true
LARK_APP_ID="your_app_id_here"
LARK_APP_SECRET="your_app_secret_here"
PHONE_NOTIFICATION_USER_IDS="user_id_1,user_id_2"
PHONE_NOTIFICATION_MESSAGE_IDS="msg_id_1,msg_id_2,msg_id_3,msg_id_4"
PHONE_NOTIFICATION_INTERVAL=15
```

## 🎵 音乐提醒功能

### 功能说明
- 检测到匹配消息时自动播放音乐
- 显示桌面弹窗提醒
- 用户点击确定后停止音乐
- 支持 MP3、WAV、OGG 格式

### 使用步骤
1. 在 `static/audio/` 目录放置音乐文件
2. 配置 `NOTIFICATION_MUSIC_FILE` 路径
3. 启用 `ENABLE_MUSIC_NOTIFICATION=true`

详细说明请查看 [音乐功能文档](docs/MUSIC_NOTIFICATION.md)

## 📞 电话提醒功能

### 功能说明
- 检测到匹配消息时并行发送飞书紧急电话提醒
- 支持多个用户同时提醒
- 支持多条消息间隔发送
- 与音乐提醒同时执行

### 使用步骤
1. 创建飞书应用并获取 App ID 和 Secret
2. 配置目标用户ID和消息ID列表
3. 启用 `ENABLE_PHONE_NOTIFICATION=true`

详细说明请查看 [电话功能文档](docs/PHONE_NOTIFICATION.md)

## 🌐 Web管理界面

### 功能说明
- 实时状态监控和统计信息显示
- 功能开关控制（消息监听、自动回复、音乐提醒、电话提醒）
- 在线配置管理，支持所有环境变量修改
- 实时日志查看和过滤

### 访问地址
```
http://127.0.0.1:8080
```

### 主要页面
1. **状态概览** (`/`) - 系统运行状态和功能开关
2. **配置管理** (`/config`) - 环境变量在线编辑
3. **运行日志** (`/logs`) - 实时日志查看和搜索

### 使用步骤
1. 启动程序后自动开启Web服务
2. 浏览器访问管理界面
3. 在配置页面设置必要参数
4. 使用开关控制各项功能
5. 通过日志页面监控运行状态

详细说明请查看 [Web界面文档](docs/WEB_INTERFACE.md)

## 🔒 安全性保护

### 敏感信息保护
- **自动脱敏**：Cookie、密钥等敏感信息自动脱敏显示
- **安全输入**：敏感字段使用密码输入框，支持可见性切换
- **无法查看**：一旦设置，敏感信息无法通过任何方式查看原始值
- **安全传输**：Web界面仅本地访问，敏感信息不在网络传输

### 安全特性
- 🛡️ 自动识别敏感配置项
- 🔐 密码字段安全输入
- 👁️ 可见性切换控制
- 🚫 脱敏值显示保护
- ✅ 配置有效性验证

详细说明请查看 [安全性文档](docs/SECURITY.md)

## 📁 项目结构

```
LarkFlow/
├── app/                    # 应用程序模块
│   ├── api/               # API 相关
│   ├── config/            # 配置管理
│   ├── core/              # 核心业务
│   └── utils/             # 工具函数
├── builder/               # 协议构建器
├── static/                # 静态资源
├── docs/                  # 文档
├── main.py               # 程序入口
└── requirements.txt      # 依赖列表
```

详细结构说明请查看 [项目结构文档](PROJECT_STRUCTURE.md)

## 🔧 开发指南

### 环境要求
- Python 3.10+
- Windows 10/11
- 网络连接
- 音频设备（音乐功能）

### 开发流程
1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 发起 Pull Request

### 代码规范
- 遵循 PEP 8
- 添加类型注解
- 编写单元测试
- 更新文档

## 📚 文档

- [项目结构说明](PROJECT_STRUCTURE.md)
- [Web管理界面文档](docs/WEB_INTERFACE.md)
- [安全性说明](docs/SECURITY.md)
- [音乐功能文档](docs/MUSIC_NOTIFICATION.md)
- [电话功能文档](docs/PHONE_NOTIFICATION.md)
- [开发指南](docs/DEVELOPMENT.md)
- [快速开始](docs/QUICK_START.md)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

### 贡献指南
1. 提交 Issue 描述问题或建议
2. Fork 项目并创建分支
3. 编写代码并测试
4. 提交 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [飞书开放平台](https://open.feishu.cn/)
- [PyInstaller](https://pyinstaller.org/)
- [pygame](https://pygame.org/)
- 所有贡献者

## 📞 支持

- 📧 Email: <EMAIL>
- 💬 Issues: [GitHub Issues](https://github.com/ljcyt/LarkFlow/issues)
- 📖 Wiki: [项目 Wiki](https://github.com/ljcyt/LarkFlow/wiki)

---

⭐ 如果这个项目对您有帮助，请给个 Star！



LarkAgentX-master - 2.0/
├── 📄 文档文件
│   ├── README.md                        # 项目主文档
│   ├── LICENSE                          # MIT 许可证
│   ├── CHANGELOG.md                     # 版本更新日志
│   ├── PROJECT_STRUCTURE.md             # 项目结构说明
│   ├── PROJECT_SUMMARY.md               # 项目整理总结
│   ├── AUDIO_FILE_MANAGER_GUIDE.md      # 音频文件管理指南
│   ├── WEB_CONTROL_GUIDE.md             # Web控制界面指南
│   ├── WEB_MUSIC_PLAYER_GUIDE.md        # Web音乐播放器指南
│   └── WEB_NOTIFICATION_DIALOG_GUIDE.md # Web通知对话框指南
│
├── 📄 核心程序文件
│   ├── main.py                          # 程序主入口
│   ├── launcher.py                      # 启动器
│   ├── quick_fix.py                     # 快速修复工具
│   └── requirements.txt                 # Python依赖列表
│
├── 📁 app/                              # 应用程序核心模块
│   ├── __init__.py
│   ├── 📁 api/                          # API 相关模块
│   │   ├── __init__.py
│   │   ├── auth.py                      # 飞书认证模块
│   │   └── lark_client.py               # 飞书客户端核心
│   ├── 📁 config/                       # 配置管理
│   │   ├── __init__.py
│   │   └── settings.py                  # 应用配置
│   ├── 📁 core/                         # 核心业务逻辑
│   │   ├── __init__.py
│   │   ├── app_service_manager.py       # 应用服务管理器
│   │   └── message_service.py           # 消息处理服务
│   ├── 📁 utils/                        # 工具函数
│   │   ├── __init__.py
│   │   ├── lark_utils.py               # 飞书工具函数
│   │   ├── music_player.py             # 音乐播放器
│   │   ├── notification.py             # 通知弹窗
│   │   └── phone_reminder.py           # 电话提醒模块
│   └── 📁 web/                          # Web管理界面
│       ├── __init__.py
│       ├── web_server.py               # Flask Web服务器
│       ├── config_manager.py           # 配置文件管理
│       ├── state_manager.py            # 应用状态管理
│       ├── 📁 templates/               # HTML模板
│       │   ├── base.html               # 基础模板
│       │   ├── index.html              # 主页面
│       │   ├── config.html             # 配置页面
│       │   ├── logs.html               # 日志页面
│       │   ├── 404.html                # 404错误页面
│       │   └── 500.html                # 500错误页面
│       └── 📁 static/                  # Web静态资源
│           └── style.css               # CSS样式文件
│
├── 📁 builder/                          # 协议构建器
│   ├── __init__.py
│   ├── header.py                       # HTTP请求头构建
│   ├── params.py                       # 请求参数构建
│   └── proto.py                        # Protocol Buffers协议解析
│
├── 📁 static/                           # 静态资源
│   ├── __init__.py
│   ├── 📁 audio/                       # 音频文件
│   │   ├── README.md                   # 音频文件说明
│   │   └── zfb100w.mp3                # 示例音频文件
│   ├── 📁 resource/                    # 图片资源
│   │   ├── back_end.png               # 后端截图
│   │   ├── front_end.png              # 前端截图
│   │   ├── front_end_1.png            # 前端截图1
│   │   ├── front_end_2.png            # 前端截图2
│   │   └── functions.png              # 功能截图
│   ├── lark_decrypt.js                # JavaScript解密工具
│   ├── proto.proto                    # Protocol Buffers定义
│   └── proto_pb2.py                   # 生成的协议文件
│
├── 📁 docs/                            # 详细文档
│   ├── DEVELOPMENT.md                  # 开发指南
│   ├── MUSIC_NOTIFICATION.md           # 音乐提醒功能说明
│   ├── PHONE_NOTIFICATION.md           # 电话提醒功能说明
│   ├── QUICK_FIX.md                   # 快速修复指南
│   ├── QUICK_START.md                 # 快速开始指南
│   ├── SECURITY.md                    # 安全性文档
│   ├── TROUBLESHOOTING.md             # 故障排除指南
│   └── WEB_INTERFACE.md               # Web界面文档
│
├── 📁 scripts/                         # 工具脚本
│   ├── __init__.py
│   ├── clean_cache.py                 # 清理缓存脚本
│   ├── manual_phone_reminder.py       # 手动电话提醒工具
│   └── setup_project.py               # 项目初始化脚本
│
└── 📁 logs/                            # 日志文件
    └── launcher.log                    # 启动器日志