# LarkAgentX 音频文件管理器

## 🎵 功能概述

LarkAgentX 现在支持通过Web界面直接上传和管理音频文件，用户可以轻松选择电脑中的音频文件作为提醒音乐。

## ✨ 新功能特性

### 📁 音频文件上传
- ✅ **拖拽上传** - 支持直接选择电脑中的音频文件
- ✅ **多格式支持** - MP3、WAV、OGG、M4A、AAC
- ✅ **文件大小限制** - 最大支持 10MB
- ✅ **自动配置** - 上传后自动更新配置文件

### 🎧 音频文件管理
- ✅ **文件列表** - 显示所有可用的音频文件
- ✅ **文件预览** - 点击播放按钮预览音频（3秒）
- ✅ **一键选择** - 快速选择为提醒音乐
- ✅ **文件信息** - 显示文件名和大小

### 🔄 实时更新
- ✅ **自动刷新** - 页面加载时自动获取文件列表
- ✅ **即时生效** - 选择文件后立即更新配置
- ✅ **状态反馈** - 操作成功/失败的即时提示

## 🚀 使用方法

### 1. 访问音频管理界面
1. 启动 LarkAgentX：`python launcher.py`
2. 打开浏览器访问：`http://127.0.0.1:8080`
3. 在首页找到"音频文件管理"区域

### 2. 上传音频文件
1. 点击"选择文件"按钮
2. 从电脑中选择音频文件（支持 MP3、WAV、OGG、M4A、AAC）
3. 点击"上传文件"按钮
4. 等待上传完成，系统会显示成功提示

### 3. 选择提醒音乐
1. 在"可用音频文件"列表中查看已上传的文件
2. 点击"预览"按钮试听音频（播放3秒）
3. 点击"选择"按钮设置为提醒音乐
4. 系统会自动更新配置并显示成功提示

### 4. 测试音乐播放
1. 确保主应用已启动
2. 发送匹配的飞书消息
3. 浏览器会自动播放选择的音频文件

## 📋 支持的音频格式

| 格式 | 扩展名 | 浏览器兼容性 | 推荐度 |
|------|--------|--------------|--------|
| MP3  | .mp3   | 所有现代浏览器 | ⭐⭐⭐⭐⭐ |
| WAV  | .wav   | 所有现代浏览器 | ⭐⭐⭐⭐ |
| OGG  | .ogg   | Firefox、Chrome | ⭐⭐⭐ |
| M4A  | .m4a   | Safari、Chrome | ⭐⭐⭐ |
| AAC  | .aac   | Safari、Chrome | ⭐⭐⭐ |

**推荐使用 MP3 格式**，兼容性最好。

## 🎯 音频文件建议

### 文件要求
- **时长**：10-30秒（会循环播放）
- **大小**：10MB以内
- **音质**：128kbps 足够
- **音量**：适中，避免过于刺耳

### 内容建议
- 选择清晰、易识别的提醒音
- 避免过于激烈或刺耳的音乐
- 可以使用语音提醒（如"您有新消息"）
- 建议使用短促、重复性强的音效

## 🔧 技术实现

### API 接口
- `POST /api/upload-audio` - 上传音频文件
- `GET /api/list-audio` - 获取音频文件列表
- `POST /api/select-audio` - 选择音频文件
- `GET /api/audio/<filename>` - 音频文件服务

### 文件存储
- 上传的文件保存在：`static/audio/` 目录
- 自动创建目录结构
- 文件名保持原始名称

### 配置更新
- 自动更新 `.env` 文件中的 `NOTIFICATION_MUSIC_FILE` 配置
- 实时重新加载应用配置
- 无需重启应用即可生效

## 🛠️ 故障排除

### 上传失败
1. **文件格式不支持**
   - 检查文件扩展名是否在支持列表中
   - 尝试转换为 MP3 格式

2. **文件过大**
   - 压缩音频文件或选择较短的片段
   - 降低音频质量（如 128kbps）

3. **网络问题**
   - 检查网络连接
   - 尝试刷新页面重新上传

### 播放失败
1. **浏览器兼容性**
   - 使用现代浏览器（Chrome、Firefox、Edge）
   - 确保浏览器支持 HTML5 Audio

2. **音频格式问题**
   - 尝试使用 MP3 格式
   - 检查音频文件是否损坏

3. **权限问题**
   - 某些浏览器需要用户交互后才能播放音频
   - 先点击页面上的任意按钮

## 🔒 安全考虑

### 文件安全
- 只允许上传音频格式文件
- 文件大小限制防止滥用
- 文件名安全检查

### 路径安全
- 防止路径遍历攻击
- 文件存储在指定目录
- 安全的文件访问控制

## 🎉 使用优势

1. **用户友好** - 无需手动编辑配置文件
2. **即时预览** - 上传前可以试听音频
3. **自动配置** - 选择后自动更新系统配置
4. **文件管理** - 统一管理所有音频文件
5. **云部署兼容** - 完美支持云服务器部署

现在您可以轻松管理音频文件，让 LarkAgentX 的音乐提醒更加个性化！🎵
