/* LarkAgentX Web管理界面自定义样式 */

/* 侧边栏样式 */
.sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    border-radius: 8px;
    margin: 2px 0;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover, 
.sidebar .nav-link.active {
    color: white;
    background-color: rgba(255,255,255,0.1);
    transform: translateX(5px);
}

/* 状态卡片样式 */
.status-card {
    border-left: 4px solid #28a745;
    transition: all 0.3s ease;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.status-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #2196F3;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* 日志容器样式 */
.log-container {
    background-color: #1e1e1e;
    color: #f8f8f2;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.4;
    border-radius: 8px;
}

.log-entry {
    padding: 2px 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.log-entry:last-child {
    border-bottom: none;
}

/* 配置分类样式 */
.config-category {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.config-category h6 {
    font-weight: 600;
    margin-bottom: 15px;
}

/* 表单样式增强 */
.form-control:focus,
.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 敏感字段样式 */
.sensitive-field {
    border-left: 3px solid #ffc107;
    background-color: #fffbf0;
}

.sensitive-field:focus {
    border-left: 3px solid #ff9800;
    background-color: #fff8e1;
}

.sensitive-field::placeholder {
    color: #ff9800;
    font-style: italic;
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 徽章样式 */
.badge {
    border-radius: 6px;
    font-size: 0.8em;
}

/* 卡片样式增强 */
.card {
    border-radius: 12px;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .status-card {
        margin-bottom: 15px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 加载动画 */
.spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误页面样式 */
.error-page {
    padding: 60px 0;
}

.error-page h1 {
    font-size: 8rem;
    font-weight: 300;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
}

.progress-bar {
    border-radius: 4px;
}

/* 自定义滚动条 */
.log-container::-webkit-scrollbar {
    width: 8px;
}

.log-container::-webkit-scrollbar-track {
    background: #2d2d2d;
    border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb:hover {
    background: #777;
}

/* 表格样式 */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    border-bottom: 2px solid #dee2e6;
    background: #f8f9fa;
}

/* 模态框样式 */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 12px 12px;
}
