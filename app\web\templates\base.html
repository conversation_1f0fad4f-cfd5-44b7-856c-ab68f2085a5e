<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}LarkAgentX 管理界面{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('favicon') }}">
    <link rel="shortcut icon" type="image/x-icon" href="{{ url_for('favicon') }}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            /* 清爽蓝色系主题 */
            --primary-color: #0ea5e9;
            --primary-light: #bae6fd;
            --primary-dark: #0284c7;
            --primary-subtle: #f0f9ff;

            /* 清淡背景色 */
            --bg-primary: #ffffff;
            --bg-secondary: #fafbfc;
            --bg-tertiary: #f1f5f9;
            --bg-subtle: #f8fafc;

            /* 柔和状态色 */
            --success-color: #22c55e;
            --success-light: #dcfce7;
            --warning-color: #f59e0b;
            --warning-light: #fef3c7;
            --danger-color: #ef4444;
            --danger-light: #fee2e2;
            --info-color: #06b6d4;
            --info-light: #cffafe;

            /* 清淡文字色 */
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --text-light: #cbd5e1;

            /* 清淡边框色 */
            --border-color: #e2e8f0;
            --border-light: #f1f5f9;
            --border-subtle: #f8fafc;

            /* 轻柔阴影 */
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.02);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.04), 0 2px 4px -2px rgb(0 0 0 / 0.04);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.06), 0 4px 6px -4px rgb(0 0 0 / 0.06);

            /* 圆角 */
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(255, 255, 255, 0.08);
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.02);
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: var(--radius-md);
            margin: 4px 0;
            padding: 12px 16px;
            font-weight: 500;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
            transform: translateX(4px);
        }

        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: var(--shadow-md);
        }

        .card {
            border: none;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            background: var(--bg-primary);
        }

        .card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .status-card {
            border-left: 3px solid var(--primary-color);
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--primary-subtle) 100%);
            border: 1px solid var(--border-light);
        }

        .btn {
            border-radius: var(--radius-md);
            font-weight: 500;
            padding: 10px 20px;
            transition: all 0.2s ease;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            box-shadow: var(--shadow-sm);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, #0369a1 100%);
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }

        .btn-outline-secondary {
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
        }

        .btn-outline-secondary:hover {
            background-color: var(--secondary-color);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 52px;
            height: 28px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--border-color);
            transition: all 0.3s ease;
            border-radius: 28px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 20px;
            width: 20px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: all 0.3s ease;
            border-radius: 50%;
            box-shadow: var(--shadow-sm);
        }

        input:checked + .slider {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        }

        input:checked + .slider:before {
            transform: translateX(24px);
        }

        .log-container {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            color: #f1f5f9;
            font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
            max-height: 400px;
            overflow-y: auto;
            border-radius: var(--radius-md);
            border: 1px solid #334155;
        }

        .alert {
            border: none;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
        }

        .badge {
            border-radius: var(--radius-sm);
            font-weight: 500;
            padding: 6px 12px;
        }

        h1, h2, h3, h4, h5, h6 {
            font-weight: 600;
            color: var(--text-primary);
        }

        .text-muted {
            color: var(--text-muted) !important;
        }

        .border-bottom {
            border-color: var(--border-color) !important;
        }

        .form-control {
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            transition: all 0.2s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
        }

        .btn-group .btn {
            border-radius: 0;
        }

        .btn-group .btn:first-child {
            border-top-left-radius: var(--radius-md);
            border-bottom-left-radius: var(--radius-md);
        }

        .btn-group .btn:last-child {
            border-top-right-radius: var(--radius-md);
            border-bottom-right-radius: var(--radius-md);
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <div class="bg-white bg-opacity-20 rounded-circle p-2 me-2">
                                <i class="fas fa-robot text-white"></i>
                            </div>
                            <h4 class="text-white mb-0 fw-bold">LarkFlow</h4>
                        </div>
                        <small class="text-white-50 fw-medium">智能消息处理系统</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'index' %}active{% endif %}" href="{{ url_for('index') }}">
                                <i class="fas fa-tachometer-alt"></i>
                                状态概览
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'config' %}active{% endif %}" href="{{ url_for('config') }}">
                                <i class="fas fa-cog"></i>
                                配置管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'logs' %}active{% endif %}" href="{{ url_for('logs') }}">
                                <i class="fas fa-file-alt"></i>
                                运行日志
                            </a>
                        </li>
                    </ul>
                    
                    <hr class="text-white-50">
                    
                    <div class="text-center">
                        <button class="btn btn-outline-light btn-sm" onclick="restartApp()">
                            <i class="fas fa-redo"></i>
                            重启应用
                        </button>
                    </div>
                </div>
            </nav>
            
            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-4 pb-3 mb-4">
                    <div>
                        <h1 class="h2 mb-1">{% block page_title %}管理界面{% endblock %}</h1>
                        <p class="text-muted mb-0">实时监控和管理您的LarkFlow系统</p>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="badge bg-success me-2" id="status-indicator">
                            <i class="fas fa-circle me-1"></i>
                            运行中
                        </span>
                        <div class="text-muted small">
                            <i class="fas fa-clock me-1"></i>
                            <span id="current-time"></span>
                        </div>
                    </div>
                </div>
                
                <!-- 消息提示 -->
                <div id="message-container"></div>
                
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 全局函数
        function showMessage(message, type = 'success') {
            const container = document.getElementById('message-container');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            container.appendChild(alertDiv);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
        
        function restartApp() {
            if (confirm('确定要重启应用程序吗？')) {
                fetch('/api/restart', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showMessage('重启请求已发送', 'info');
                    } else {
                        showMessage('重启失败: ' + data.error, 'danger');
                    }
                })
                .catch(error => {
                    showMessage('请求失败: ' + error, 'danger');
                });
            }
        }
        
        // 定期更新状态
        function updateStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    const indicator = document.getElementById('status-indicator');
                    if (data.running) {
                        indicator.innerHTML = '<i class="fas fa-circle"></i> 运行中';
                        indicator.className = 'badge bg-success';
                    } else {
                        indicator.innerHTML = '<i class="fas fa-circle"></i> 已停止';
                        indicator.className = 'badge bg-danger';
                    }
                })
                .catch(error => {
                    console.error('更新状态失败:', error);
                });
        }
        
        // 每5秒更新一次状态
        setInterval(updateStatus, 5000);
        
        // 更新当前时间
        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // 页面加载完成后立即更新状态和时间
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
            updateCurrentTime();
            // 每秒更新时间
            setInterval(updateCurrentTime, 1000);
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
