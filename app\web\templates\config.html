{% extends "base.html" %}

{% block page_title %}配置管理{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-cog"></i>
                    环境配置管理
                </h5>
                <div>
                    <button class="btn btn-primary" onclick="saveConfig()">
                        <i class="fas fa-save"></i>
                        保存配置
                    </button>
                    <button class="btn btn-secondary" onclick="resetConfig()">
                        <i class="fas fa-undo"></i>
                        重置
                    </button>
                </div>
            </div>
            <div class="card-body">
                <form id="config-form">
                    {% set categories = {} %}
                    {% for key, info in schema.items() %}
                        {% set category = info.category %}
                        {% if category not in categories %}
                            {% set _ = categories.update({category: []}) %}
                        {% endif %}
                        {% set _ = categories[category].append((key, info)) %}
                    {% endfor %}
                    
                    {% for category, items in categories.items() %}
                    <div class="config-category mb-4">
                        <h6 class="text-primary border-bottom pb-2">
                            <i class="fas fa-folder"></i>
                            {{ category }}
                        </h6>
                        
                        <div class="row">
                            {% for key, info in items %}
                            <div class="col-md-6 mb-3" id="config-item-{{ key }}"
                                 {% if key in ['TRIGGER_PATTERN', 'REPLY_MESSAGE'] %}data-mode="content"{% endif %}
                                 {% if key in ['GROUP_NAME_PATTERN', 'GROUP_NAME_CONTENT_PATTERN', 'GROUP_NAME_REPLY_MESSAGE'] %}data-mode="group_name"{% endif %}>
                                <label for="{{ key }}" class="form-label">
                                    {{ info.description }}
                                    {% if info.required %}
                                        <span class="text-danger">*</span>
                                    {% endif %}
                                </label>
                                
                                {% if info.type == 'boolean' %}
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox"
                                               id="{{ key }}" name="{{ key }}"
                                               {% if config.get(key, info.get('default', 'false')).lower() == 'true' %}checked{% endif %}>
                                        <label class="form-check-label" for="{{ key }}">
                                            启用
                                        </label>
                                    </div>
                                {% elif info.type == 'select' %}
                                    <select class="form-select" id="{{ key }}" name="{{ key }}">
                                        {% set current_value = config.get(key, info.get('default', '')) %}
                                        {% for option in info.options %}
                                            <option value="{{ option.value }}"
                                                    {% if option.value == current_value %}selected{% endif %}>
                                                {{ option.label }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                {% elif info.type == 'textarea' %}
                                    <textarea class="form-control" id="{{ key }}" name="{{ key }}"
                                              rows="3" placeholder="{{ info.get('default', '') }}">{{ config.get(key, info.get('default', '')) }}</textarea>
                                {% elif info.type == 'password' %}
                                    <div class="input-group">
                                        <input type="password" class="form-control sensitive-field"
                                               id="{{ key }}" name="{{ key }}"
                                               value="{{ config.get(key, info.get('default', '')) }}"
                                               placeholder="{% if config.get(key) %}点击修改已设置的值{% else %}{{ info.get('default', '') }}{% endif %}"
                                               data-is-sensitive="true">
                                        <button class="btn btn-outline-secondary" type="button"
                                                onclick="togglePassword('{{ key }}')">
                                            <i class="fas fa-eye" id="{{ key }}-eye"></i>
                                        </button>
                                    </div>
                                    {% if config.get(key) and config.get(key) != info.get('default', '') %}
                                        <small class="text-success">
                                            <i class="fas fa-check-circle"></i>
                                            已设置（输入新值可覆盖）
                                        </small>
                                    {% endif %}
                                {% elif info.type == 'number' %}
                                    <input type="number" class="form-control"
                                           id="{{ key }}" name="{{ key }}"
                                           value="{{ config.get(key, info.get('default', '')) }}"
                                           placeholder="{{ info.get('default', '') }}">
                                {% else %}
                                    <input type="text" class="form-control"
                                           id="{{ key }}" name="{{ key }}"
                                           value="{{ config.get(key, info.get('default', '')) }}"
                                           placeholder="{{ info.get('default', '') }}">
                                {% endif %}
                                
                                {% if info.get('help') %}
                                    <div class="form-text">{{ info.help }}</div>
                                {% endif %}
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endfor %}
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 配置预览 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-file-code"></i>
                    配置预览 (.env)
                </h6>
            </div>
            <div class="card-body">
                <pre id="config-preview" class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;"></pre>
            </div>
        </div>
    </div>
</div>

<!-- 配置验证状态 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-check-circle"></i>
                    配置验证状态
                </h6>
            </div>
            <div class="card-body">
                <div id="validation-status">
                    <div class="text-center text-muted">
                        <i class="fas fa-spinner fa-spin"></i>
                        检查配置状态中...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let originalConfig = {};

    // 页面加载时保存原始配置
    document.addEventListener('DOMContentLoaded', function() {
        saveOriginalConfig();
        updateConfigPreview();
        loadValidationStatus();
        updateModeVisibility(); // 初始化模式显示

        // 监听表单变化
        document.getElementById('config-form').addEventListener('input', updateConfigPreview);
        document.getElementById('config-form').addEventListener('change', function(e) {
            updateConfigPreview();
            // 如果是消息匹配模式改变，更新显示
            if (e.target.name === 'MESSAGE_MATCH_MODE') {
                updateModeVisibility();
            }
        });
    });

    function saveOriginalConfig() {
        const form = document.getElementById('config-form');
        originalConfig = {};

        // 获取所有输入字段
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            if (input.type === 'checkbox') {
                originalConfig[input.name] = input.checked ? 'true' : 'false';
            } else {
                originalConfig[input.name] = input.value;
            }
        });
    }

    function getCurrentConfig() {
        const form = document.getElementById('config-form');
        const config = {};

        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            if (input.type === 'checkbox') {
                config[input.name] = input.checked ? 'true' : 'false';
            } else {
                // 对于敏感字段，如果值是脱敏的（包含*），则跳过
                const value = input.value;
                const isSensitive = input.dataset.isSensitive === 'true';

                if (isSensitive && (value.includes('*') || value === '***已设置***')) {
                    // 敏感字段未修改，不包含在更新中
                    return;
                }

                config[input.name] = value;
            }
        });

        return config;
    }

    function updateModeVisibility() {
        const modeSelect = document.getElementById('MESSAGE_MATCH_MODE');
        if (!modeSelect) return;

        const currentMode = modeSelect.value;

        // 获取所有带有data-mode属性的配置项
        const contentModeItems = document.querySelectorAll('[data-mode="content"]');
        const groupNameModeItems = document.querySelectorAll('[data-mode="group_name"]');

        if (currentMode === 'content') {
            // 显示内容匹配模式的配置项
            contentModeItems.forEach(item => {
                item.style.display = 'block';
            });
            // 隐藏群聊名称匹配模式的配置项
            groupNameModeItems.forEach(item => {
                item.style.display = 'none';
            });
        } else if (currentMode === 'group_name') {
            // 隐藏内容匹配模式的配置项
            contentModeItems.forEach(item => {
                item.style.display = 'none';
            });
            // 显示群聊名称匹配模式的配置项
            groupNameModeItems.forEach(item => {
                item.style.display = 'block';
            });
        }
    }

    function updateConfigPreview() {
        const config = getCurrentConfig();
        let preview = '';

        for (const [key, value] of Object.entries(config)) {
            if (value) {
                preview += `${key}="${value}"\n`;
            }
        }

        document.getElementById('config-preview').textContent = preview;
    }

    function saveConfig() {
        const config = getCurrentConfig();

        fetch('/api/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('配置保存成功！某些配置可能需要重启应用后生效。', 'success');
                saveOriginalConfig(); // 更新原始配置
                loadValidationStatus(); // 重新验证配置
            } else {
                showMessage('配置保存失败: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            showMessage('保存失败: ' + error, 'danger');
        });
    }

    function resetConfig() {
        if (confirm('确定要重置所有配置到上次保存的状态吗？')) {
            const form = document.getElementById('config-form');
            const inputs = form.querySelectorAll('input, textarea, select');

            inputs.forEach(input => {
                const originalValue = originalConfig[input.name] || '';

                if (input.type === 'checkbox') {
                    input.checked = originalValue.toLowerCase() === 'true';
                } else {
                    input.value = originalValue;
                }
            });

            updateConfigPreview();
            updateModeVisibility(); // 重置后更新显示
            showMessage('配置已重置', 'info');
        }
    }

    function togglePassword(fieldId) {
        const field = document.getElementById(fieldId);
        const eye = document.getElementById(fieldId + '-eye');

        if (field.type === 'password') {
            field.type = 'text';
            eye.className = 'fas fa-eye-slash';
        } else {
            field.type = 'password';
            eye.className = 'fas fa-eye';
        }
    }

    function loadValidationStatus() {
        fetch('/api/config')
            .then(response => response.json())
            .then(data => {
                if (data.validation) {
                    displayValidationStatus(data.validation);
                }
            })
            .catch(error => {
                console.error('加载验证状态失败:', error);
                document.getElementById('validation-status').innerHTML =
                    '<div class="text-danger"><i class="fas fa-exclamation-triangle"></i> 加载验证状态失败</div>';
            });
    }

    function displayValidationStatus(validation) {
        const container = document.getElementById('validation-status');
        let html = '<div class="row">';

        // 音乐文件状态
        html += '<div class="col-md-6 mb-3">';
        html += '<div class="d-flex align-items-center">';
        if (validation.music_file_exists) {
            html += '<i class="fas fa-check-circle text-success me-2"></i>';
            html += '<span class="text-success">音乐文件配置正确</span>';
        } else {
            html += '<i class="fas fa-times-circle text-danger me-2"></i>';
            html += '<span class="text-danger">音乐文件不存在</span>';
        }
        html += '</div>';
        if (validation.music_file_path) {
            html += `<small class="text-muted">路径: ${validation.music_file_path}</small>`;
        }
        html += '</div>';

        // 飞书Cookie状态
        html += '<div class="col-md-6 mb-3">';
        html += '<div class="d-flex align-items-center">';
        if (validation.lark_cookie_configured) {
            html += '<i class="fas fa-check-circle text-success me-2"></i>';
            html += '<span class="text-success">飞书Cookie已配置</span>';
        } else {
            html += '<i class="fas fa-times-circle text-danger me-2"></i>';
            html += '<span class="text-danger">飞书Cookie未配置</span>';
        }
        html += '</div></div>';

        // 电话提醒状态
        html += '<div class="col-md-6 mb-3">';
        html += '<div class="d-flex align-items-center">';
        if (validation.phone_config_complete) {
            html += '<i class="fas fa-check-circle text-success me-2"></i>';
            html += '<span class="text-success">电话提醒配置完整</span>';
        } else {
            html += '<i class="fas fa-exclamation-triangle text-warning me-2"></i>';
            html += '<span class="text-warning">电话提醒配置不完整</span>';
        }
        html += '</div></div>';

        html += '</div>';

        // 错误信息
        if (validation.errors && validation.errors.length > 0) {
            html += '<div class="alert alert-danger mt-3">';
            html += '<h6><i class="fas fa-exclamation-triangle"></i> 配置错误</h6>';
            html += '<ul class="mb-0">';
            validation.errors.forEach(error => {
                html += `<li>${error}</li>`;
            });
            html += '</ul></div>';
        }

        // 警告信息
        if (validation.warnings && validation.warnings.length > 0) {
            html += '<div class="alert alert-warning mt-3">';
            html += '<h6><i class="fas fa-info-circle"></i> 配置提醒</h6>';
            html += '<ul class="mb-0">';
            validation.warnings.forEach(warning => {
                html += `<li>${warning}</li>`;
            });
            html += '</ul></div>';
        }

        container.innerHTML = html;
    }
</script>
{% endblock %}
