#!/usr/bin/env python3
"""
验证音频权限修改是否正确
"""
import os
import re

def verify_audio_permission_changes():
    """验证音频权限相关的修改"""
    print("=== 验证音频权限修改 ===\n")
    
    html_file = "app/web/templates/index.html"
    
    if not os.path.exists(html_file):
        print(f"❌ 文件不存在: {html_file}")
        return False
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = []
    
    # 检查1: audioPermissionGranted 默认为 true
    if "let audioPermissionGranted = true;" in content:
        print("✅ 音频权限默认设置为 true")
        checks.append(True)
    else:
        print("❌ 音频权限默认设置未修改")
        checks.append(False)
    
    # 检查2: checkAudioPermission 函数简化
    if "console.log('音频权限默认启用');" in content:
        print("✅ 权限检查函数已简化")
        checks.append(True)
    else:
        print("❌ 权限检查函数未简化")
        checks.append(False)
    
    # 检查3: attemptAudioPlayback 移除权限检查
    permission_check_pattern = r"if\s*\(\s*!audioPermissionGranted\s*\)"
    if not re.search(permission_check_pattern, content):
        print("✅ 音频播放函数已移除权限检查")
        checks.append(True)
    else:
        print("❌ 音频播放函数仍有权限检查")
        checks.append(False)
    
    # 检查4: 错误处理不重置权限
    if "audioPermissionGranted = false;" not in content:
        print("✅ 错误处理不再重置权限状态")
        checks.append(True)
    else:
        print("❌ 错误处理仍会重置权限状态")
        checks.append(False)
    
    # 检查5: 页面提示信息更新
    if "音频权限默认开启" in content:
        print("✅ 页面提示信息已更新")
        checks.append(True)
    else:
        print("❌ 页面提示信息未更新")
        checks.append(False)
    
    return all(checks)

def verify_music_player_changes():
    """验证音乐播放器修改"""
    print("\n=== 验证音乐播放器修改 ===\n")
    
    music_player_file = "app/utils/music_player.py"
    
    if not os.path.exists(music_player_file):
        print(f"❌ 文件不存在: {music_player_file}")
        return False
    
    with open(music_player_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = []
    
    # 检查支持重复触发
    if "停止当前播放，启动新的音乐播放" in content:
        print("✅ 音乐播放器支持重复触发")
        checks.append(True)
    else:
        print("❌ 音乐播放器不支持重复触发")
        checks.append(False)
    
    return all(checks)

def verify_message_service_changes():
    """验证消息服务修改"""
    print("\n=== 验证消息服务修改 ===\n")
    
    service_file = "app/core/message_service.py"
    
    if not os.path.exists(service_file):
        print(f"❌ 文件不存在: {service_file}")
        return False
    
    with open(service_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = []
    
    # 检查直接调用音乐播放器
    if "music_player.start_playing(music_file)" in content:
        print("✅ 消息服务直接调用音乐播放器")
        checks.append(True)
    else:
        print("❌ 消息服务未直接调用音乐播放器")
        checks.append(False)
    
    return all(checks)

def main():
    """主验证函数"""
    print("开始验证音频权限和音乐播放功能修改...\n")
    
    results = []
    
    # 验证各个组件的修改
    results.append(verify_audio_permission_changes())
    results.append(verify_music_player_changes())
    results.append(verify_message_service_changes())
    
    print("\n" + "="*50)
    print("验证结果汇总:")
    print("="*50)
    
    component_names = [
        "音频权限设置",
        "音乐播放器",
        "消息服务"
    ]
    
    for i, (name, result) in enumerate(zip(component_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
    
    if all(results):
        print(f"\n🎉 所有验证通过！")
        print("\n修改总结:")
        print("- ✅ 音频权限默认启用，无需用户手动操作")
        print("- ✅ 移除了复杂的权限检查逻辑")
        print("- ✅ 音乐可以直接播放，无权限阻拦")
        print("- ✅ 简化了用户界面和交互流程")
        print("- ✅ 保持了原有的音乐播放功能")
        
        print(f"\n🚀 现在您可以测试音乐播放功能:")
        print("1. 启动应用")
        print("2. 触发消息匹配")
        print("3. 音乐应该直接开始播放，无需权限确认")
        print("4. 页面刷新可停止音乐")
    else:
        print(f"\n❌ 部分验证失败，请检查修改")

if __name__ == "__main__":
    main()
