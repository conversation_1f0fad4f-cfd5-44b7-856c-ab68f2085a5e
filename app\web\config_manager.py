"""
配置管理器
用于读取、修改和保存.env配置文件
"""
import os
import re
from typing import Dict, Any, Optional
from pathlib import Path
from loguru import logger


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, env_file: str = ".env"):
        self.env_file = Path(env_file)
        self.config_cache = {}
        self.load_config()
    
    def load_config(self) -> Dict[str, str]:
        """加载配置文件"""
        config = {}
        
        if not self.env_file.exists():
            logger.warning(f"配置文件不存在: {self.env_file}")
            return config
        
        try:
            with open(self.env_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # 跳过空行和注释
                    if not line or line.startswith('#'):
                        continue
                    
                    # 解析键值对
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip().strip('"\'')
                        config[key] = value
                    
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
        
        self.config_cache = config
        return config
    
    def get_config(self, key: str, default: str = "") -> str:
        """获取配置值"""
        value = self.config_cache.get(key, default)

        # 敏感信息脱敏处理
        if self._is_sensitive_key(key) and value:
            return self._mask_sensitive_value(value)

        return value
    
    def get_all_config(self) -> Dict[str, str]:
        """获取所有配置（敏感信息已脱敏）"""
        masked_config = {}
        for key, value in self.config_cache.items():
            if self._is_sensitive_key(key) and value:
                masked_config[key] = self._mask_sensitive_value(value)
            else:
                masked_config[key] = value
        return masked_config

    def get_raw_config(self, key: str, default: str = "") -> str:
        """获取原始配置值（仅内部使用，不脱敏）"""
        return self.config_cache.get(key, default)
    
    def set_config(self, key: str, value: str) -> bool:
        """设置配置值"""
        try:
            self.config_cache[key] = value
            return True
        except Exception as e:
            logger.error(f"设置配置失败: {str(e)}")
            return False
    
    def save_config(self) -> bool:
        """保存配置到文件"""
        try:
            # 读取原文件内容，保持注释和格式
            original_lines = []
            if self.env_file.exists():
                with open(self.env_file, 'r', encoding='utf-8') as f:
                    original_lines = f.readlines()
            
            # 构建新的文件内容
            new_lines = []
            processed_keys = set()
            
            # 处理原有行，更新已存在的配置
            for line in original_lines:
                stripped = line.strip()
                
                # 保持注释和空行
                if not stripped or stripped.startswith('#'):
                    new_lines.append(line)
                    continue
                
                # 处理配置行
                if '=' in stripped:
                    key = stripped.split('=', 1)[0].strip()
                    if key in self.config_cache:
                        # 更新配置值
                        new_lines.append(f'{key}="{self.config_cache[key]}"\n')
                        processed_keys.add(key)
                    else:
                        # 保持原行
                        new_lines.append(line)
                else:
                    new_lines.append(line)
            
            # 添加新的配置项
            for key, value in self.config_cache.items():
                if key not in processed_keys:
                    new_lines.append(f'{key}="{value}"\n')
            
            # 写入文件
            with open(self.env_file, 'w', encoding='utf-8') as f:
                f.writelines(new_lines)
            
            logger.info("配置文件保存成功")
            return True
            
        except Exception as e:
            logger.error(f"保存配置文件失败: {str(e)}")
            return False
    
    def get_config_schema(self) -> Dict[str, Dict[str, Any]]:
        """获取配置项的结构定义"""
        return {
            "LARK_COOKIE": {
                "type": "password",
                "required": True,
                "description": "飞书Cookie（必须）",
                "category": "认证配置"
            },
            "MESSAGE_MATCH_MODE": {
                "type": "select",
                "required": False,
                "description": "消息匹配模式",
                "category": "消息配置",
                "default": "content",
                "options": [
                    {"value": "content", "label": "内容匹配模式"},
                    {"value": "group_name", "label": "群聊名称匹配模式"}
                ]
            },
            "TRIGGER_PATTERN": {
                "type": "text",
                "required": False,
                "description": "内容匹配正则表达式",
                "category": "消息配置",
                "default": "已接通人工.*?@.*?为你服务.*?请问.*?帮你"
            },
            "REPLY_MESSAGE": {
                "type": "text",
                "required": False,
                "description": "内容匹配回复内容",
                "category": "消息配置",
                "default": "您好！有什么可以帮您？"
            },
            "GROUP_NAME_PATTERN": {
                "type": "text",
                "required": False,
                "description": "群聊名称匹配正则表达式",
                "category": "消息配置",
                "default": ".*'s 门店IT服务台"
            },
            "GROUP_NAME_CONTENT_PATTERN": {
                "type": "text",
                "required": False,
                "description": "群聊名称模式下的消息内容匹配正则表达式",
                "category": "消息配置",
                "default": "^None$"
            },
            "GROUP_NAME_REPLY_MESSAGE": {
                "type": "text",
                "required": False,
                "description": "群聊名称匹配回复内容",
                "category": "消息配置",
                "default": "您好！我是IT服务台智能助手，有什么可以帮您？"
            },
            "ENABLE_MUSIC_NOTIFICATION": {
                "type": "boolean",
                "required": False,
                "description": "启用音乐提醒",
                "category": "提醒配置",
                "default": "true"
            },
            "NOTIFICATION_MUSIC_FILE": {
                "type": "text",
                "required": False,
                "description": "音乐文件路径", 
                "category": "提醒配置",
                "default": "static/audio/notification.mp3"
            },
            "NOTIFICATION_TITLE": {
                "type": "text",
                "required": False,
                "description": "弹窗标题",
                "category": "提醒配置",
                "default": "飞书消息提醒"
            },
            "NOTIFICATION_MESSAGE": {
                "type": "textarea",
                "required": False,
                "description": "弹窗消息内容",
                "category": "提醒配置",
                "default": "您有新的飞书消息！\\n点击确定停止音乐提醒"
            },
            "ENABLE_PHONE_NOTIFICATION": {
                "type": "boolean", 
                "required": False,
                "description": "启用电话提醒",
                "category": "电话配置",
                "default": "true"
            },
            "LARK_APP_ID": {
                "type": "text",
                "required": False,
                "description": "飞书应用ID",
                "category": "电话配置"
            },
            "LARK_APP_SECRET": {
                "type": "password",
                "required": False, 
                "description": "飞书应用密钥",
                "category": "电话配置"
            },
            "PHONE_NOTIFICATION_USER_IDS": {
                "type": "text",
                "required": False,
                "description": "电话提醒用户ID列表（逗号分隔）",
                "category": "电话配置"
            },
            "PHONE_NOTIFICATION_MESSAGE_IDS": {
                "type": "text", 
                "required": False,
                "description": "电话提醒消息ID列表（逗号分隔）",
                "category": "电话配置"
            },
            "PHONE_NOTIFICATION_INTERVAL": {
                "type": "number",
                "required": False,
                "description": "电话提醒间隔（秒）",
                "category": "电话配置",
                "default": "15"
            }
        }

    def _is_sensitive_key(self, key: str) -> bool:
        """判断是否为敏感配置项"""
        sensitive_keys = {
            'LARK_COOKIE',
            'LARK_APP_SECRET',
            'OPENAI_API_KEY',
            'PASSWORD',
            'SECRET',
            'TOKEN',
            'KEY'
        }

        # 精确匹配或包含敏感关键词
        key_upper = key.upper()
        return (key_upper in sensitive_keys or
                any(sensitive in key_upper for sensitive in ['SECRET', 'PASSWORD', 'TOKEN', 'KEY', 'COOKIE']))

    def _mask_sensitive_value(self, value: str) -> str:
        """脱敏处理敏感值"""
        if not value or len(value) <= 8:
            return "***已设置***"

        # 显示前3位和后3位，中间用*代替
        return f"{value[:3]}{'*' * (len(value) - 6)}{value[-3:]}"


# 全局配置管理器实例
config_manager = ConfigManager()
