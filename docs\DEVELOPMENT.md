# 开发指南

本文档为 LarkFlow 项目的开发者提供详细的开发指南。

## 🛠️ 开发环境设置

### 系统要求
- **操作系统**: Windows 10/11, macOS, Linux
- **Python**: 3.10 或更高版本
- **内存**: 至少 4GB RAM
- **存储**: 至少 1GB 可用空间

### 环境准备

1. **克隆项目**
```bash
git clone https://github.com/your-repo/LarkFlow.git
cd LarkFlow
```

2. **创建虚拟环境**
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，填入必要配置
```

## 📁 项目结构详解

### 核心模块

#### app/api/
- **auth.py**: 飞书认证管理
  - `get_auth()`: 获取认证信息
  - Cookie 解析和验证
- **lark_client.py**: 飞书客户端核心
  - `LarkClient`: 主要客户端类
  - WebSocket 连接管理
  - 消息发送和接收

#### app/core/
- **message_service.py**: 消息处理服务
  - `MessageService`: 消息处理主类
  - 模式匹配和自动回复
  - 音乐提醒集成

#### app/utils/
- **music_player.py**: 音乐播放功能
  - `MusicPlayer`: 音乐播放器类
  - 循环播放和停止控制
- **notification.py**: 通知弹窗
  - `NotificationDialog`: 弹窗管理
  - `MusicNotification`: 音乐通知集成

#### builder/
- **proto.py**: 协议解析
  - Protocol Buffers 消息解析
  - 飞书协议适配
- **params.py**: 参数构建
  - WebSocket 连接参数
  - HTTP 请求参数

## 🔧 开发工作流

### 1. 功能开发

1. **创建功能分支**
```bash
git checkout -b feature/new-feature
```

2. **编写代码**
- 遵循 PEP 8 代码规范
- 添加类型注解
- 编写文档字符串

3. **测试代码**
```bash
python -m pytest tests/
```

4. **提交代码**
```bash
git add .
git commit -m "feat: add new feature"
```

### 2. 代码规范

#### Python 代码风格
```python
from typing import Optional, Dict, Any
import asyncio

class ExampleClass:
    """示例类的文档字符串"""
    
    def __init__(self, config: Dict[str, Any]) -> None:
        self.config = config
    
    async def process_message(self, message: str) -> Optional[str]:
        """
        处理消息的异步方法
        
        Args:
            message: 输入消息
            
        Returns:
            处理后的消息，如果无需处理则返回 None
        """
        if not message:
            return None
        
        # 处理逻辑
        return f"Processed: {message}"
```

#### 命名规范
- **类名**: PascalCase (如 `MessageService`)
- **函数名**: snake_case (如 `process_message`)
- **常量**: UPPER_SNAKE_CASE (如 `DEFAULT_TIMEOUT`)
- **私有方法**: 以下划线开头 (如 `_internal_method`)

### 3. 测试指南

#### 单元测试
```python
import pytest
from app.core.message_service import MessageService

class TestMessageService:
    def test_message_processing(self):
        service = MessageService(mock_client)
        result = service.process_message("test message")
        assert result is not None
```

#### 集成测试
```python
@pytest.mark.asyncio
async def test_websocket_connection():
    client = LarkClient(auth)
    await client.connect_websocket(mock_handler)
    assert client.is_connected()
```

## 🚀 部署和打包

### 开发环境运行
```bash
python main.py
```

### 生产环境打包
```bash
# Windows EXE 打包
python build_exe.py

# 或使用批处理
build.bat
```

## 🐛 调试指南

### 日志配置
```python
from loguru import logger

# 开发环境：详细日志
logger.add("debug.log", level="DEBUG")

# 生产环境：错误日志
logger.add("error.log", level="ERROR")
```

### 常见问题

#### 1. WebSocket 连接失败
- 检查网络连接
- 验证 Cookie 是否有效
- 确认飞书服务状态

#### 2. 音乐播放问题
- 检查音频文件格式
- 验证 pygame 安装
- 确认音频设备可用

#### 3. 打包失败
- 检查 PyInstaller 版本
- 验证依赖完整性
- 查看打包日志

## 📚 API 文档

### LarkClient 主要方法

```python
class LarkClient:
    async def connect_websocket(self, handler: Callable) -> None:
        """连接 WebSocket"""
        
    def send_msg(self, content: str, chat_id: str) -> requests.Response:
        """发送消息"""
        
    def get_self_user_info(self) -> Tuple[bool, str]:
        """获取用户信息"""
```

### MessageService 主要方法

```python
class MessageService:
    async def process_message(
        self, 
        user_name: str, 
        user_id: str, 
        content: str, 
        is_group_chat: bool, 
        group_name: str, 
        chat_id: str
    ) -> None:
        """处理接收到的消息"""
```

## 🤝 贡献指南

### 提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

- `feat`: 新功能
- `fix`: 问题修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### Pull Request 流程

1. Fork 项目
2. 创建功能分支
3. 编写代码和测试
4. 更新文档
5. 提交 Pull Request
6. 代码审查
7. 合并到主分支

## 📈 性能优化

### 内存优化
- 使用生成器处理大量数据
- 及时释放不需要的对象
- 避免循环引用

### 网络优化
- 实现连接池
- 添加重试机制
- 使用异步 I/O

### 并发优化
- 合理使用 asyncio
- 避免阻塞操作
- 使用线程池处理 CPU 密集任务

## 🔒 安全考虑

### 敏感信息保护
- 不在代码中硬编码密钥
- 使用环境变量管理配置
- 定期轮换认证信息

### 输入验证
- 验证所有外部输入
- 防止注入攻击
- 限制请求频率

## 📊 监控和日志

### 日志级别
- **DEBUG**: 详细调试信息
- **INFO**: 一般信息
- **WARNING**: 警告信息
- **ERROR**: 错误信息
- **CRITICAL**: 严重错误

### 监控指标
- 消息处理延迟
- WebSocket 连接状态
- 内存使用情况
- 错误率统计
