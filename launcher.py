#!/usr/bin/env python3
"""
LarkAgentX Web启动器
提供web界面来控制整个LarkAgentX应用的启动和停止
"""
import sys
import os
import time
import signal
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """设置日志配置"""
    # 移除默认的日志处理器
    logger.remove()
    
    # 添加控制台日志 - 简洁美观的输出格式
    logger.add(
        sys.stdout,
        format="<dim>{time:MM-DD HH:mm:ss}</dim> <level>[{level:^5}]</level> {message}",
        level="INFO"
    )
    
    # 添加文件日志
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    logger.add(
        "logs/launcher.log",
        format="{time:YYYY-MM-DD HH:mm:ss} [{level:^5}] {message}",
        level="DEBUG",
        rotation="10 MB",
        retention="7 days",
        compression="zip"
    )

def check_environment():
    """检查运行环境"""
    logger.info("检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        logger.error("Python版本过低，需要Python 3.8或更高版本")
        return False
    
    # 检查必要的模块
    required_modules = ['flask', 'loguru', 'asyncio']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        logger.error(f"缺少必要的模块: {', '.join(missing_modules)}")
        logger.error("请运行: pip install -r requirements.txt")
        return False
    
    # 检查配置文件
    if not os.path.exists('.env'):
        logger.warning("未找到.env配置文件，将使用默认配置")
        logger.info("建议复制.env.example为.env并配置相关参数")
    
    logger.info("环境检查通过")
    return True

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}，正在退出...")
    sys.exit(0)

def main():
    """主函数"""
    print("LarkAgentX Web控制器")
    print("=" * 50)
    
    # 设置日志
    setup_logging()
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 导入web服务器
        from app.web.web_server import web_server
        from app.web.state_manager import state_manager
        
        logger.info("初始化Web控制器...")
        
        # 设置Web服务器启动状态
        state_manager.start_application()
        
        # 启动Web服务器
        logger.info("启动Web管理界面...")
        web_server.start()
        
        # 显示访问信息
        print("\n" + "=" * 50)
        print("Web控制界面已启动")
        print("访问地址: http://127.0.0.1:8080")
        print("=" * 50)
        print("\n使用说明:")
        print("1. 在浏览器中打开上述地址")
        print("2. 点击'启动应用'按钮启动LarkAgentX主功能")
        print("3. 启动后可以控制各个功能开关")
        print("4. 点击'停止应用'按钮停止主功能")
        print("5. 按 Ctrl+C 退出Web控制器")
        print("\n注意: 请先在配置页面设置飞书Cookie等必要参数")
        print("=" * 50)
        
        # 保持运行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("收到中断信号，正在退出...")
        
    except ImportError as e:
        logger.error(f"导入模块失败: {str(e)}")
        logger.error("请确保已安装所有依赖: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        logger.error(f"启动失败: {str(e)}")
        sys.exit(1)
    finally:
        try:
            # 停止Web服务器
            web_server.stop()
            # 停止状态管理器
            state_manager.stop_application()
            logger.info("Web控制器已退出")
        except:
            pass

if __name__ == "__main__":
    main()
