{% extends "base.html" %}

{% block page_title %}状态概览{% endblock %}

{% block extra_css %}
<style>
    .metric-card {
        background: linear-gradient(135deg, var(--bg-primary) 0%, #fafbff 100%);
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
    }

    .metric-card:hover {
        border-color: var(--primary-color);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
    }

    .metric-number {
        font-size: 2rem;
        font-weight: 700;
        line-height: 1;
    }

    .metric-label {
        font-size: 0.875rem;
        font-weight: 500;
        opacity: 0.8;
    }

    .status-indicator {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: var(--radius-md);
        font-weight: 500;
        font-size: 0.875rem;
    }

    .status-running {
        background: linear-gradient(135deg, var(--success-color) 0%, #16a34a 100%);
        color: white;
        box-shadow: 0 2px 4px rgba(34, 197, 94, 0.2);
    }

    .status-stopped {
        background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
        color: white;
        box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
    }

    .status-connected {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: white;
        box-shadow: 0 2px 4px rgba(14, 165, 233, 0.2);
    }

    .status-disconnected {
        background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
        color: white;
        box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2);
    }

    .control-section {
        background: var(--bg-primary);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-lg);
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--shadow-sm);
        transition: all 0.2s ease;
    }

    .control-section:hover {
        box-shadow: var(--shadow-md);
        border-color: var(--border-color);
    }

    .section-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-title i {
        color: var(--primary-color);
    }

    .switch-container {
        background: var(--bg-subtle);
        border-radius: var(--radius-md);
        padding: 1rem;
        border: 1px solid var(--border-light);
        transition: all 0.2s ease;
    }

    .switch-container:hover {
        background: var(--bg-secondary);
        border-color: var(--border-color);
    }

    .audio-upload-area {
        border: 2px dashed var(--border-color);
        border-radius: var(--radius-md);
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        background: var(--bg-subtle);
    }

    .audio-upload-area:hover {
        border-color: var(--primary-color);
        background: var(--primary-subtle);
    }

    .file-list {
        background: var(--bg-subtle);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-md);
        max-height: 200px;
        overflow-y: auto;
    }

    .file-item {
        padding: 0.75rem;
        border-bottom: 1px solid var(--border-light);
        transition: all 0.2s ease;
    }

    .file-item:last-child {
        border-bottom: none;
    }

    .file-item:hover {
        background: var(--primary-subtle);
    }

    .btn-icon {
        width: 2rem;
        height: 2rem;
        padding: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }

    /* 播放状态样式 */
    .playback-status {
        padding: 8px 12px;
        border-radius: 6px;
        font-weight: 500;
        display: inline-block;
        min-width: 120px;
        text-align: center;
        font-size: 0.9em;
    }

    .playback-status.playing {
        background-color: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }

    .playback-status.stopped {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .playback-status.blocked {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .playback-status.failed,
    .playback-status.error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .playback-status.unknown {
        background-color: #e2e3e5;
        color: #383d41;
        border: 1px solid #d6d8db;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <!-- 系统状态 -->
    <div class="col-md-6 mb-4">
        <div class="card metric-card h-100">
            <div class="card-body">
                <div class="section-title">
                    <i class="fas fa-server"></i>
                    系统状态
                </div>
                <div class="row">
                    <div class="col-6">
                        <div class="mb-3">
                            <div class="fw-semibold mb-2">运行状态</div>
                            <div id="running-status">
                                {% if status.running %}
                                    <span class="status-indicator status-running">
                                        <i class="fas fa-play-circle"></i>
                                        运行中
                                    </span>
                                {% else %}
                                    <span class="status-indicator status-stopped">
                                        <i class="fas fa-stop-circle"></i>
                                        已停止
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="text-muted small" id="uptime">
                            <i class="fas fa-clock me-1"></i>
                            {{ status.uptime_formatted }}
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="mb-3">
                            <div class="fw-semibold mb-2">WebSocket连接</div>
                            <div id="websocket-status">
                                {% if status.websocket_connected %}
                                    <span class="status-indicator status-connected">
                                        <i class="fas fa-link"></i>
                                        已连接
                                    </span>
                                {% else %}
                                    <span class="status-indicator status-disconnected">
                                        <i class="fas fa-unlink"></i>
                                        未连接
                                    </span>
                                {% endif %}
                            </div>
                            <div id="connection-quality" class="small text-muted mt-1">
                                连接质量: <span id="quality-indicator">检测中...</span>
                            </div>
                            <div id="connection-stats" class="small text-muted">
                                <div>重连次数: <span id="reconnect-count">0</span></div>
                                <div>运行时间: <span id="uptime-display">--</span></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="fw-semibold mb-2">音乐播放状态</div>
                            <div id="playback-status" class="playback-status unknown">
                                🔇 等待中...
                            </div>
                            <!-- 音频权限请求按钮 -->
                            <div id="audio-permission-container" class="mt-2" style="display: none;">
                                <button id="enable-audio-btn" class="btn btn-warning btn-sm w-100 mb-2" onclick="requestAudioPermission()">
                                    <i class="fas fa-volume-up"></i> 启用音频通知
                                </button>
                                <small class="text-muted d-block">点击启用后，系统可以自动播放音乐提醒</small>
                            </div>
                            <div class="mt-2">
                                <button id="test-music-btn" class="btn btn-primary btn-sm me-2" onclick="testMusicNotification()">
                                    <i class="fas fa-play"></i> 测试音乐播放
                                </button>
                                <button id="stop-music-btn" class="btn btn-secondary btn-sm" onclick="stopMusicNotification()">
                                    <i class="fas fa-stop"></i> 停止播放
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息统计 -->
    <div class="col-md-6 mb-4">
        <div class="card metric-card h-100">
            <div class="card-body">
                <div class="section-title">
                    <i class="fas fa-chart-bar"></i>
                    消息统计
                </div>
                <div class="row text-center">
                    <div class="col-3">
                        <div class="mb-2">
                            <div class="metric-number text-success" id="message-count">{{ status.total_messages }}</div>
                            <div class="metric-label text-muted">接收</div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="mb-2">
                            <div class="metric-number text-primary" id="reply-count">{{ status.total_replies }}</div>
                            <div class="metric-label text-muted">回复</div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="mb-2">
                            <div class="metric-number text-warning" id="music-notification-count">{{ status.total_music_notifications }}</div>
                            <div class="metric-label text-muted">音乐</div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="mb-2">
                            <div class="metric-number text-info" id="phone-notification-count">{{ status.total_phone_notifications }}</div>
                            <div class="metric-label text-muted">电话</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 应用控制 -->
<div class="control-section">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <div class="section-title">
                <i class="fas fa-power-off"></i>
                应用控制
            </div>
            <p class="text-muted mb-0" id="main-app-status-text">
                {% if status.main_app_running %}
                    LarkFlow 主功能正在运行
                {% else %}
                    LarkFlow 主功能已停止
                {% endif %}
            </p>
        </div>
        <div class="d-flex gap-2">
            {% if status.main_app_running %}
                <button class="btn btn-danger" id="stop-app-btn" onclick="stopMainApp()">
                    <i class="fas fa-stop me-1"></i> 停止应用
                </button>
            {% else %}
                <button class="btn btn-success" id="start-app-btn" onclick="startMainApp()">
                    <i class="fas fa-play me-1"></i> 启动应用
                </button>
            {% endif %}
            <button class="btn btn-outline-secondary" onclick="validateCookie()">
                <i class="fas fa-check-circle me-1"></i> 检查Cookie
            </button>
        </div>
    </div>
</div>

<!-- 功能开关控制 -->
<div class="control-section">
    <div class="section-title">
        <i class="fas fa-toggle-on"></i>
        功能开关
    </div>
    <div class="row">
        <div class="col-md-6 col-lg-3 mb-3">
            <div class="switch-container">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="fw-semibold">消息监听</div>
                    <label class="switch">
                        <input type="checkbox" id="switch-message-listening"
                               {% if status.message_listening_enabled %}checked{% endif %}
                               onchange="toggleSwitch('message_listening', this.checked)">
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-3 mb-3">
            <div class="switch-container">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="fw-semibold">自动回复</div>
                    <label class="switch">
                        <input type="checkbox" id="switch-auto-reply"
                               {% if status.auto_reply_enabled %}checked{% endif %}
                               onchange="toggleSwitch('auto_reply', this.checked)">
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-3 mb-3">
            <div class="switch-container">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="fw-semibold">音乐提醒</div>
                    <label class="switch">
                        <input type="checkbox" id="switch-music-notification"
                               {% if status.music_notification_enabled %}checked{% endif %}
                               onchange="toggleSwitch('music_notification', this.checked)">
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-3 mb-3">
            <div class="switch-container">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="fw-semibold">电话提醒</div>
                    <label class="switch">
                        <input type="checkbox" id="switch-phone-notification"
                               {% if status.phone_notification_enabled %}checked{% endif %}
                               onchange="toggleSwitch('phone_notification', this.checked)">
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 音频文件管理 -->
<div class="control-section">
    <div class="section-title">
        <i class="fas fa-music"></i>
        音频文件管理
    </div>
    <div class="row">
        <div class="col-md-6">
            <div class="audio-upload-area mb-3">
                <div class="mb-3">
                    <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                    <div class="fw-semibold mb-2">上传音频文件</div>
                    <input type="file" class="form-control" id="audio-file-input" accept=".mp3,.wav,.ogg,.m4a,.aac">
                    <small class="text-muted mt-2 d-block">支持格式: MP3, WAV, OGG, M4A, AAC (最大 10MB)</small>
                </div>
            </div>
            <div class="btn-group w-100" role="group">
                <button class="btn btn-primary" onclick="uploadAudioFile()">
                    <i class="fas fa-upload me-1"></i> 上传
                </button>
                <button class="btn btn-outline-secondary" onclick="refreshAudioList()">
                    <i class="fas fa-refresh me-1"></i> 刷新
                </button>
                <button class="btn btn-outline-warning" onclick="testMusicPlay()">
                    <i class="fas fa-play me-1"></i> 测试
                </button>
                <button class="btn btn-outline-info" onclick="testSystemNotification()">
                    <i class="fas fa-bell me-1"></i> 弹窗
                </button>
            </div>
        </div>
        <div class="col-md-6">
            <div class="fw-semibold mb-2">可用音频文件</div>
            <div id="audio-files-list" class="file-list">
                <div class="text-center text-muted p-3">
                    <i class="fas fa-spinner fa-spin me-1"></i> 加载中...
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 错误信息 -->
{% if status.last_error %}
<div class="row mt-4">
    <div class="col-12">
        {% if 'Cookie' in status.last_error or 'cookie' in status.last_error or '过期' in status.last_error %}
        <div class="alert alert-warning">
            <h6><i class="fas fa-exclamation-triangle"></i> Cookie相关错误</h6>
            <p class="mb-2">{{ status.last_error }}</p>
            <div class="d-flex justify-content-between align-items-center">
                <small class="text-muted">错误次数: {{ status.error_count }}</small>
                <a href="/config" class="btn btn-warning btn-sm">
                    <i class="fas fa-cog"></i> 前往配置页面更新Cookie
                </a>
            </div>
        </div>
        {% else %}
        <div class="alert alert-danger">
            <h6><i class="fas fa-exclamation-triangle"></i> 最近错误</h6>
            <p class="mb-0">{{ status.last_error }}</p>
            <small class="text-muted">错误次数: {{ status.error_count }}</small>
        </div>
        {% endif %}
    </div>
</div>
{% endif %}

<!-- 隐藏的音频播放器 -->
<audio id="notification-audio" preload="auto" style="display: none;">
    <source id="audio-source" src="" type="audio/mpeg">
    您的浏览器不支持音频播放。
</audio>

<!-- 音乐提醒弹窗 -->
<div class="modal fade" id="musicNotificationModal" tabindex="-1" aria-labelledby="musicNotificationModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="musicNotificationModalLabel">
                    <i class="fas fa-music"></i> 飞书消息提醒
                </h5>
            </div>
            <div class="modal-body text-center">
                <div class="mb-3">
                    <i class="fas fa-bell fa-3x text-warning"></i>
                </div>
                <h6 id="notification-message">您有新的飞书消息！</h6>
                <p class="text-muted">🎵 音乐提醒正在播放...</p>
                <div class="mt-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">播放中...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success btn-lg w-100" onclick="stopMusicNotification()">
                    <i class="fas fa-check"></i> 确定 - 停止音乐提醒
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    function toggleSwitch(switchName, enabled) {
        fetch(`/api/toggle/${switchName}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ enabled: enabled })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(`${getSwitchDisplayName(switchName)} ${enabled ? '已启用' : '已禁用'}`, 'success');
            } else {
                showMessage('操作失败: ' + data.error, 'danger');
                // 恢复开关状态
                document.getElementById(`switch-${switchName.replace('_', '-')}`).checked = !enabled;
            }
        })
        .catch(error => {
            showMessage('请求失败: ' + error, 'danger');
            // 恢复开关状态
            document.getElementById(`switch-${switchName.replace('_', '-')}`).checked = !enabled;
        });
    }
    
    function getSwitchDisplayName(switchName) {
        const names = {
            'message_listening': '消息监听',
            'auto_reply': '自动回复',
            'music_notification': '音乐提醒',
            'phone_notification': '电话提醒'
        };
        return names[switchName] || switchName;
    }

    // 启动主应用
    function startMainApp() {
        const button = document.getElementById('start-app-btn');
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 启动中...';

        fetch('/api/app/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('主应用启动成功', 'success');
                updateAppControlUI(true);
            } else {
                const errorMsg = data.error || '未知错误';
                // 检查是否是Cookie相关错误
                if (errorMsg.toLowerCase().includes('cookie') ||
                    errorMsg.includes('过期') ||
                    errorMsg.includes('认证失败') ||
                    errorMsg.includes('unauthorized')) {
                    showMessage('启动失败: ' + errorMsg + ' - 建议前往配置页面更新Cookie', 'warning');
                } else {
                    showMessage('启动失败: ' + errorMsg, 'danger');
                }
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-play"></i> 启动应用';
            }
        })
        .catch(error => {
            showMessage('请求失败: ' + error, 'danger');
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-play"></i> 启动应用';
        });
    }

    // 停止主应用
    function stopMainApp() {
        const button = document.getElementById('stop-app-btn');
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 停止中...';

        fetch('/api/app/stop', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('主应用停止成功', 'success');
                updateAppControlUI(false);
            } else {
                showMessage('停止失败: ' + data.error, 'danger');
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-stop"></i> 停止应用';
            }
        })
        .catch(error => {
            showMessage('请求失败: ' + error, 'danger');
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-stop"></i> 停止应用';
        });
    }

    // 更新应用控制UI
    function updateAppControlUI(isRunning) {
        const statusText = document.getElementById('main-app-status-text');
        const container = document.querySelector('#start-app-btn, #stop-app-btn').parentElement;

        if (isRunning) {
            statusText.textContent = 'LarkFlow 主功能正在运行';
            container.innerHTML = '<button class="btn btn-danger" id="stop-app-btn" onclick="stopMainApp()"><i class="fas fa-stop"></i> 停止应用</button>';
        } else {
            statusText.textContent = 'LarkFlow 主功能已停止';
            container.innerHTML = '<button class="btn btn-success" id="start-app-btn" onclick="startMainApp()"><i class="fas fa-play"></i> 启动应用</button>';
        }
    }

    // 验证Cookie状态
    function validateCookie() {
        const button = event.target;
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 检查中...';

        fetch('/api/validate-cookie', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.valid) {
                showMessage('Cookie验证成功，可以正常使用', 'success');
            } else {
                showMessage('Cookie验证失败: ' + data.message, 'warning');
            }
        })
        .catch(error => {
            showMessage('Cookie验证请求失败: ' + error, 'danger');
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = originalText;
        });
    }

    
    // 每1.5秒更新一次状态数据（提高响应速度）
    setInterval(updateStatusData, 1500);

    // 更新连接状态信息
    function updateConnectionStatus() {
        fetch('/api/connection-info')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateConnectionDisplay(data.connection_info);
                }
            })
            .catch(error => {
                console.error('获取连接信息失败:', error);
            });
    }

    function updateConnectionDisplay(info) {
        // 更新连接质量显示
        const qualityIndicator = document.getElementById('quality-indicator');
        const reconnectCount = document.getElementById('reconnect-count');
        const uptimeDisplay = document.getElementById('uptime-display');

        if (qualityIndicator) {
            let qualityText = '';
            let qualityClass = '';

            switch(info.quality) {
                case 'good':
                    qualityText = '良好';
                    qualityClass = 'text-success';
                    break;
                case 'poor':
                    qualityText = '较差';
                    qualityClass = 'text-warning';
                    break;
                case 'disconnected':
                    qualityText = '已断开';
                    qualityClass = 'text-danger';
                    break;
                default:
                    qualityText = '检测中';
                    qualityClass = 'text-muted';
            }

            qualityIndicator.textContent = qualityText;
            qualityIndicator.className = qualityClass;
        }

        // 更新重连次数
        if (reconnectCount) {
            reconnectCount.textContent = info.reconnect_count || 0;
            if (info.reconnect_count > 0) {
                reconnectCount.parentElement.className = 'text-warning';
            } else {
                reconnectCount.parentElement.className = 'text-muted';
            }
        }

        // 更新运行时间
        if (uptimeDisplay && info.uptime) {
            const hours = Math.floor(info.uptime / 3600);
            const minutes = Math.floor((info.uptime % 3600) / 60);
            uptimeDisplay.textContent = `${hours}h ${minutes}m`;
        }
    }

    // 每5秒更新一次连接状态
    setInterval(updateConnectionStatus, 5000);

    // 页面加载时刷新音频文件列表
    document.addEventListener('DOMContentLoaded', function() {
        // 页面加载时停止任何正在播放的音乐
        stopAudioPlayback();

        refreshAudioList();
        showMessage('✅ 音乐提醒已启用 - 正在检测音频权限 - 页面刷新可停止音乐', 'info');

        // 检查音频播放权限
        checkAudioPermission();
    });

    // 音频文件管理功能
    function uploadAudioFile() {
        const fileInput = document.getElementById('audio-file-input');
        const file = fileInput.files[0];

        if (!file) {
            showMessage('请选择一个音频文件', 'warning');
            return;
        }

        // 检查文件大小 (10MB)
        if (file.size > 10 * 1024 * 1024) {
            showMessage('文件大小不能超过 10MB', 'warning');
            return;
        }

        const formData = new FormData();
        formData.append('audio_file', file);

        // 显示上传进度
        const uploadBtn = event.target;
        const originalText = uploadBtn.innerHTML;
        uploadBtn.disabled = true;
        uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 上传中...';

        fetch('/api/upload-audio', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('音频文件上传成功: ' + data.filename, 'success');
                fileInput.value = ''; // 清空文件选择
                refreshAudioList(); // 刷新文件列表
            } else {
                showMessage('上传失败: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            showMessage('上传请求失败: ' + error, 'danger');
        })
        .finally(() => {
            uploadBtn.disabled = false;
            uploadBtn.innerHTML = originalText;
        });
    }

    function refreshAudioList() {
        const listContainer = document.getElementById('audio-files-list');
        listContainer.innerHTML = '<div class="text-center text-muted"><i class="fas fa-spinner fa-spin"></i> 加载中...</div>';

        fetch('/api/list-audio')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayAudioFiles(data.files);
                } else {
                    listContainer.innerHTML = '<div class="text-danger">加载失败: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                listContainer.innerHTML = '<div class="text-danger">请求失败: ' + error + '</div>';
            });
    }

    function displayAudioFiles(files) {
        const listContainer = document.getElementById('audio-files-list');

        if (files.length === 0) {
            listContainer.innerHTML = '<div class="text-muted text-center p-3"><i class="fas fa-music fa-2x mb-2 d-block"></i>暂无音频文件<br><small>请上传音频文件</small></div>';
            return;
        }

        let html = '';
        files.forEach(file => {
            html += `
                <div class="file-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1">
                            <div class="fw-semibold">${file.filename}</div>
                            <small class="text-muted">
                                <i class="fas fa-file-audio me-1"></i>
                                ${file.size_mb} MB
                            </small>
                        </div>
                        <div class="d-flex gap-1">
                            <button class="btn btn-icon btn-outline-primary" onclick="previewAudio('${file.path}')" title="预览">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="btn btn-sm btn-success" onclick="selectAudioFile('${file.filename}')" title="选择为提醒音乐">
                                <i class="fas fa-check me-1"></i>选择
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });

        listContainer.innerHTML = html;
    }

    function selectAudioFile(filename) {
        fetch('/api/select-audio', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ filename: filename })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('音频文件选择成功: ' + filename, 'success');
            } else {
                showMessage('选择失败: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            showMessage('请求失败: ' + error, 'danger');
        });
    }

    function previewAudio(audioPath) {
        const audioUrl = `/api/audio/${audioPath}`;
        const audio = new Audio(audioUrl);

        audio.play().then(() => {
            showMessage('🎵 正在预览音频...', 'info');

            // 3秒后停止预览
            setTimeout(() => {
                audio.pause();
                audio.currentTime = 0;
            }, 3000);
        }).catch(error => {
            showMessage('预览失败: ' + error.message, 'warning');
        });
    }

    function testMusicPlay() {
        // 手动创建一个测试播放事件
        const testEvent = {
            action: 'start',
            music_file: 'static/audio/zfb100w.mp3', // 使用当前配置的音频文件
            timestamp: Date.now() / 1000
        };

        console.log('手动测试播放事件:', testEvent);
        handleMusicPlayEvent(testEvent);

        // 5秒后停止
        setTimeout(() => {
            const stopEvent = {
                action: 'stop',
                music_file: 'static/audio/zfb100w.mp3',
                timestamp: Date.now() / 1000
            };
            console.log('手动测试停止事件:', stopEvent);
            handleMusicPlayEvent(stopEvent);
        }, 5000);
    }

    function testSystemNotification() {
        // 创建测试弹窗事件
        const testDialogEvent = {
            action: 'show',
            title: '测试系统弹窗',
            message: '这是一个测试消息，用于验证系统confirm弹窗功能是否正常工作。',
            timestamp: Date.now() / 1000
        };

        console.log('手动测试系统弹窗事件:', testDialogEvent);
        showMessage('🔔 即将显示系统confirm弹窗', 'info');

        // 延迟一下让用户看到提示消息
        setTimeout(() => {
            handleDialogEvent(testDialogEvent);
        }, 500);
    }

    // 音频播放控制
    let currentAudio = null;
    let lastMusicEventId = null;
    let lastDialogEventId = null;
    let audioPermissionGranted = true; // 默认启用音频权限

    function handleMusicPlayEvent(eventData) {
        console.log('收到音乐播放事件:', eventData);

        if (!eventData) {
            console.log('跳过空事件');
            return;
        }

        // 为音乐事件生成唯一ID：类型+时间戳+动作
        const eventId = `music_${eventData.timestamp}_${eventData.action}`;

        if (eventId === lastMusicEventId) {
            console.log('跳过重复音乐事件');
            return;
        }

        lastMusicEventId = eventId;

        if (eventData.action === 'start') {
            // 纯音乐播放模式，不显示弹窗
            console.log('开始纯音乐播放模式');
            handleAudioPlayback(eventData);
        } else if (eventData.action === 'stop') {
            console.log('停止音乐播放');
            stopAudioPlayback();
            // 停止事件时清除音乐事件
            clearMusicEvent();
        }
    }

    // handleUnifiedNotification 函数已移除（纯音乐播放模式不需要）

    // 音频权限管理 - 页面加载时主动获取权限
    function checkAudioPermission() {
        console.log('检查并获取音频权限...');

        // 尝试播放静音音频来获取权限
        const audio = document.getElementById('notification-audio');
        if (audio) {
            // 设置极小音量，几乎静音
            audio.volume = 0.01;
            audio.muted = false;

            const playPromise = audio.play();
            if (playPromise !== undefined) {
                playPromise.then(() => {
                    console.log('音频权限获取成功');
                    audioPermissionGranted = true;
                    audio.pause();
                    audio.currentTime = 0;
                    audio.volume = 1; // 恢复正常音量
                    hideAudioPermissionButton();
                    updatePlaybackStatus('ready', '🎵 音频通知已启用');
                    return true;
                }).catch(error => {
                    console.log('音频权限获取失败，需要用户交互:', error);
                    audioPermissionGranted = false;
                    showAudioPermissionButton();
                    updatePlaybackStatus('permission-needed', '🔇 需要启用音频权限');
                    return false;
                });
            }
        }
        return audioPermissionGranted;
    }

    function showAudioPermissionButton() {
        console.log('显示音频权限请求按钮');
        const container = document.getElementById('audio-permission-container');
        if (container) {
            container.style.display = 'block';
            updatePlaybackStatus('permission-needed', '🔇 需要启用音频权限');
        }
    }

    function hideAudioPermissionButton() {
        console.log('隐藏音频权限请求按钮');
        const container = document.getElementById('audio-permission-container');
        if (container) {
            container.style.display = 'none';
        }
    }

    function requestAudioPermission() {
        console.log('用户请求音频权限...');
        const button = document.getElementById('enable-audio-btn');
        const originalText = button.innerHTML;

        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 启用中...';

        // 播放一段静音音频来获取权限
        const audio = document.getElementById('notification-audio');
        console.log('音频元素:', audio);

        if (audio) {
            console.log('找到音频元素，开始设置权限获取...');

            // 检查音频源
            const audioSource = document.getElementById('audio-source');
            console.log('音频源元素:', audioSource);
            console.log('当前音频源 src:', audioSource ? audioSource.src : 'null');

            // 如果没有音频源，设置一个默认的
            if (!audioSource.src || audioSource.src === '' || audioSource.src === window.location.origin + '/') {
                console.log('设置默认音频源...');
                audioSource.src = '/api/audio/static/audio/zfb100w.mp3';
                audio.load(); // 重新加载音频
                console.log('默认音频源已设置:', audioSource.src);
            } else {
                console.log('音频源已存在，无需设置');
            }

            // 设置静音播放
            audio.volume = 0.01; // 极小音量，几乎静音
            audio.muted = false;
            console.log('音频设置完成，开始播放...');

            try {
                const playPromise = audio.play();
                console.log('audio.play() 返回:', playPromise);
                if (playPromise !== undefined) {
                    console.log('playPromise 不是 undefined，处理 Promise...');

                    // 添加超时机制，防止 Promise 永远不 resolve
                    const timeoutPromise = new Promise((_, reject) => {
                        setTimeout(() => {
                            reject(new Error('音频播放超时'));
                        }, 5000); // 5秒超时
                    });

                    Promise.race([playPromise, timeoutPromise])
                        .then(() => {
                            console.log('Promise resolved - 音频权限获取成功');
                            audioPermissionGranted = true;
                            audio.pause();
                            audio.currentTime = 0;
                            audio.volume = 1; // 恢复正常音量

                            hideAudioPermissionButton();
                            updatePlaybackStatus('ready', '🎵 音频通知已启用');
                            showMessage('✅ 音频通知已启用，现在可以自动播放音乐', 'success');

                            // 恢复按钮状态
                            button.disabled = false;
                            button.innerHTML = originalText;
                            console.log('按钮状态已恢复 - 成功');

                        }).catch(error => {
                            console.error('Promise rejected - 音频权限获取失败:', error);
                            audioPermissionGranted = false;
                            updatePlaybackStatus('permission-failed', '❌ 权限获取失败');
                            showMessage('❌ 音频权限获取失败: ' + error.message, 'danger');

                            button.disabled = false;
                            button.innerHTML = originalText;
                            console.log('按钮状态已恢复 - 失败');
                        });
                } else {
                    // playPromise 是 undefined，直接认为成功
                    console.log('playPromise 是 undefined，认为权限获取成功');
                    audioPermissionGranted = true;
                    audio.volume = 1; // 恢复正常音量

                    hideAudioPermissionButton();
                    updatePlaybackStatus('ready', '🎵 音频通知已启用');
                    showMessage('✅ 音频通知已启用，现在可以自动播放音乐', 'success');

                    // 恢复按钮状态
                    button.disabled = false;
                    button.innerHTML = originalText;
                    console.log('按钮状态已恢复 - undefined 情况');
                }
            } catch (error) {
                console.error('catch 块 - 音频播放调用失败:', error);
                audioPermissionGranted = false;
                updatePlaybackStatus('permission-failed', '❌ 权限获取失败');
                showMessage('❌ 音频权限获取失败: ' + error.message, 'danger');

                // 恢复按钮状态
                button.disabled = false;
                button.innerHTML = originalText;
                console.log('按钮状态已恢复 - catch 块');
            }
        } else {
            // 找不到音频元素
            console.error('音频元素为 null 或 undefined');
            updatePlaybackStatus('error', '❌ 音频元素未找到');
            showMessage('❌ 音频元素未找到，请刷新页面重试', 'danger');

            // 恢复按钮状态
            button.disabled = false;
            button.innerHTML = originalText;
            console.log('按钮状态已恢复 - 音频元素不存在');
        }

        console.log('requestAudioPermission 函数执行完毕');
    }

    function attemptAudioPlayback(musicFile, retryCount = 0) {
        const maxRetries = 3;
        const audio = document.getElementById('notification-audio');
        const audioSource = document.getElementById('audio-source');

        try {
            console.log(`尝试播放音频 (第${retryCount + 1}次):`, musicFile);

            // 停止当前播放的音频
            if (currentAudio) {
                currentAudio.pause();
                currentAudio.currentTime = 0;
            }

            // 设置新的音频源
            const audioUrl = `/api/audio/${musicFile}`;
            console.log('音频URL:', audioUrl);

            audioSource.src = audioUrl;
            audio.load(); // 重新加载音频

            // 设置循环播放
            audio.loop = true;

            // 尝试播放音频
            const playPromise = audio.play();

            if (playPromise !== undefined) {
                playPromise.then(() => {
                    console.log('音频播放成功:', musicFile);
                    currentAudio = audio;
                    updatePlaybackStatus('playing', '🎵 音乐正在播放 - 刷新页面可停止');
                }).catch(error => {
                    console.error('音频播放失败:', error);

                    // 如果是自动播放被阻止，检查权限状态
                    if (error.name === 'NotAllowedError') {
                        console.log('自动播放被阻止，检查权限状态');
                        if (!audioPermissionGranted) {
                            // 权限未获得，显示权限按钮
                            console.log('音频权限未获得，显示权限请求按钮');
                            showAudioPermissionButton();
                            updatePlaybackStatus('permission-needed', '🔇 需要启用音频权限');
                        } else if (retryCount < maxRetries) {
                            // 权限已获得但播放失败，尝试重试
                            console.log('权限已获得，尝试重试播放');
                            updatePlaybackStatus('blocked', '🔇 自动播放被阻止，正在重试...');
                            setTimeout(() => {
                                attemptAudioPlayback(musicFile, retryCount + 1);
                            }, 1000 * (retryCount + 1));
                        } else {
                            updatePlaybackStatus('failed', '❌ 音频播放失败');
                        }
                    } else if (retryCount < maxRetries) {
                        // 其他错误，尝试重试
                        console.log(`播放失败，${1000 * (retryCount + 1)}ms后重试...`);
                        setTimeout(() => {
                            attemptAudioPlayback(musicFile, retryCount + 1);
                        }, 1000 * (retryCount + 1));
                    } else {
                        updatePlaybackStatus('failed', '❌ 音频播放失败');
                        showManualPlayButton(musicFile);
                    }
                });
            }

        } catch (error) {
            console.error('音频播放设置失败:', error);
            if (retryCount < maxRetries) {
                setTimeout(() => {
                    attemptAudioPlayback(musicFile, retryCount + 1);
                }, 1000 * (retryCount + 1));
            } else {
                updatePlaybackStatus('error', '❌ 音频设置失败');
                showManualPlayButton(musicFile);
            }
        }
    }

    function handleAudioPlayback(eventData) {
        // 传统的音频播放处理（保留兼容性）
        attemptAudioPlayback(eventData.music_file);
    }

    function stopAudioPlayback() {
        try {
            console.log('停止播放音乐');
            if (currentAudio) {
                // 停止循环播放
                currentAudio.loop = false;
                // 暂停播放
                currentAudio.pause();
                // 重置播放位置
                currentAudio.currentTime = 0;
                // 清除引用
                currentAudio = null;
                console.log('音乐已停止');
                updatePlaybackStatus('stopped', '🔇 音乐已停止');
            }
        } catch (error) {
            console.error('停止音频播放失败:', error);
        }
    }

    // 更新播放状态显示
    function updatePlaybackStatus(status, message) {
        try {
            const playbackStatus = document.getElementById('playback-status');
            if (playbackStatus) {
                playbackStatus.className = `playback-status ${status}`;
                playbackStatus.textContent = message;
                console.log('播放状态已更新:', status, message);
            }
        } catch (error) {
            console.error('更新播放状态失败:', error);
        }
    }

    // 修改状态更新函数，添加音乐事件处理
    function updateStatusData() {
        fetch('/api/status')
            .then(response => response.json())
            .then(data => {
                console.log('状态数据:', data); // 调试信息

                // 更新状态指示器
                const runningStatus = document.getElementById('running-status');
                if (data.running) {
                    runningStatus.innerHTML = '<span class="status-indicator status-running"><i class="fas fa-play-circle"></i>运行中</span>';
                } else {
                    runningStatus.innerHTML = '<span class="status-indicator status-stopped"><i class="fas fa-stop-circle"></i>已停止</span>';
                }

                const uptimeElement = document.getElementById('uptime');
                uptimeElement.innerHTML = `<i class="fas fa-clock me-1"></i>${data.uptime_formatted}`;

                const websocketStatus = document.getElementById('websocket-status');
                if (data.websocket_connected) {
                    websocketStatus.innerHTML = '<span class="status-indicator status-connected"><i class="fas fa-link"></i>已连接</span>';
                } else {
                    websocketStatus.innerHTML = '<span class="status-indicator status-disconnected"><i class="fas fa-unlink"></i>未连接</span>';
                }

                document.getElementById('message-count').textContent = data.total_messages;
                document.getElementById('reply-count').textContent = data.total_replies;
                document.getElementById('music-notification-count').textContent = data.total_music_notifications;
                document.getElementById('phone-notification-count').textContent = data.total_phone_notifications;

                document.getElementById('switch-message-listening').checked = data.message_listening_enabled;
                document.getElementById('switch-auto-reply').checked = data.auto_reply_enabled;
                document.getElementById('switch-music-notification').checked = data.music_notification_enabled;
                document.getElementById('switch-phone-notification').checked = data.phone_notification_enabled;

                updateAppControlUI(data.main_app_running);

                // 处理音乐播放事件
                console.log('音乐播放事件:', data.music_play_event); // 调试信息
                if (data.music_play_event) {
                    handleMusicPlayEvent(data.music_play_event);
                } else {
                    console.log('没有音乐播放事件');
                }

                // 弹窗事件处理已移除（纯音乐播放模式）
                console.log('弹窗事件:', data.dialog_event); // 调试信息（已禁用）
            })
            .catch(error => {
                console.error('更新状态数据失败:', error);
            });
    }

    // 弹窗事件处理
    function handleDialogEvent(eventData) {
        console.log('收到弹窗事件:', eventData);

        if (!eventData) {
            console.log('跳过空弹窗事件');
            return;
        }

        // 为弹窗事件生成唯一ID：类型+时间戳+动作
        const eventId = `dialog_${eventData.timestamp}_${eventData.action}`;

        if (eventId === lastDialogEventId) {
            console.log('跳过重复弹窗事件');
            return;
        }

        lastDialogEventId = eventId;

        if (eventData.action === 'show' || eventData.action === 'start') {
            // 处理显示弹窗事件（包括统一事件）
            showSystemNotification(eventData);
            // 不在这里清除弹窗事件，让弹窗保持显示状态
        } else if (eventData.action === 'hide') {
            hideAllNotifications();
            // 隐藏事件时清除弹窗事件
            clearDialogEvent();
        }
    }

    // 显示系统弹窗（使用confirm对话框）
    function showSystemNotification(eventData) {
        try {
            const title = eventData.title || '飞书消息提醒';
            const message = eventData.message || '您有新的飞书消息！';
            const musicFile = eventData.music_file;

            // 如果有音乐文件，先尝试播放音乐
            if (musicFile && eventData.auto_play) {
                console.log('统一通知：先尝试播放音乐，再显示弹窗');
                attemptAudioPlayback(musicFile);

                // 短暂延迟后显示弹窗，确保音乐播放尝试完成
                setTimeout(() => {
                    showConfirmDialog(title, message, musicFile);
                }, 300);
            } else {
                // 没有音乐文件，直接显示弹窗
                showConfirmDialog(title, message, musicFile);
            }

        } catch (error) {
            console.error('显示系统弹窗失败:', error);
            showMessage('系统弹窗显示失败，使用网页弹窗', 'warning');
            showModalNotification(eventData);
        }
    }

    function showConfirmDialog(title, message, musicFile) {
        // 构建增强的弹窗消息
        let confirmMessage = `${title}\n\n${message}`;

        if (musicFile) {
            if (currentAudio && !currentAudio.paused) {
                confirmMessage += '\n\n🎵 音乐正在播放中...\n\n点击"确定"停止音乐提醒\n点击"取消"继续播放';
            } else {
                confirmMessage += '\n\n🔇 音乐播放可能被阻止\n\n点击"确定"停止提醒\n点击"取消"尝试手动播放';
            }
        } else {
            confirmMessage += '\n\n点击"确定"关闭提醒';
        }

        console.log('显示系统confirm弹窗:', title);
        showMessage('🔔 系统弹窗已显示', 'info');

        // 显示confirm弹窗
        const result = confirm(confirmMessage);

        if (result === true) {
            console.log('用户点击了"确定"按钮');
            stopMusicNotification();
        } else {
            console.log('用户点击了"取消"按钮');

            // 用户点击取消，检查音乐播放状态
            if (musicFile) {
                if (!currentAudio || currentAudio.paused) {
                    console.log('音乐未播放，显示手动播放按钮');
                    showManualPlayButton(musicFile);
                } else {
                    showMessage('🎵 音乐继续播放中...', 'info');
                }
            }
        }
    }

    function showManualPlayButton(musicFile) {
        console.log('显示手动播放按钮');

        // 检查是否已经有播放按钮
        const existingButton = document.getElementById('manual-play-button');
        if (existingButton) {
            existingButton.remove();
        }

        // 创建手动播放按钮
        const playButton = document.createElement('button');
        playButton.id = 'manual-play-button';
        playButton.innerHTML = '🎵 点击播放音乐';
        playButton.className = 'btn btn-primary btn-lg';
        playButton.style.cssText = 'position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999; padding: 15px 30px; font-size: 18px; box-shadow: 0 4px 8px rgba(0,0,0,0.3);';

        playButton.onclick = function() {
            console.log('用户手动点击播放');
            attemptAudioPlayback(musicFile);
            playButton.remove();
            showMessage('🎵 正在尝试播放音乐...', 'info');
        };

        document.body.appendChild(playButton);

        // 15秒后自动移除按钮
        setTimeout(() => {
            if (document.body.contains(playButton)) {
                playButton.remove();
                console.log('手动播放按钮已自动移除');
            }
        }, 15000);

        showMessage('🎵 点击屏幕中央的按钮播放音乐', 'warning');
    }

    // 显示模态框通知（备选方案）
    function showModalNotification(eventData) {
        try {
            const modal = new bootstrap.Modal(document.getElementById('musicNotificationModal'));

            // 更新弹窗内容
            document.getElementById('musicNotificationModalLabel').innerHTML =
                '<i class="fas fa-music"></i> ' + (eventData.title || '飞书消息提醒');
            document.getElementById('notification-message').textContent =
                eventData.message || '您有新的飞书消息！';

            modal.show();
            console.log('显示模态框音乐提醒弹窗');
        } catch (error) {
            console.error('显示模态框失败:', error);
            showMessage('弹窗显示失败: ' + error.message, 'danger');
        }
    }

    // 隐藏所有通知
    function hideAllNotifications() {
        // confirm弹窗会自动关闭，无需手动处理
        console.log('隐藏通知事件（confirm弹窗自动处理）');

        // 隐藏模态框（如果有的话）
        const modalElement = document.getElementById('musicNotificationModal');
        const modal = bootstrap.Modal.getInstance(modalElement);
        if (modal) {
            modal.hide();
            console.log('隐藏模态框音乐提醒弹窗');
        }
    }

    // 停止音乐提醒（用户点击确定按钮）
    function stopMusicNotification() {
        console.log('用户点击停止音乐提醒');

        // 立即停止前端音频播放
        stopAudioPlayback();

        // 清除音乐事件和弹窗事件，停止重复播放
        clearMusicEvent();
        clearDialogEvent();

        // 发送停止请求到服务器
        fetch('/api/stop-music-notification', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('音乐提醒停止成功');
                showMessage('🔇 音乐提醒已停止', 'success');
            } else {
                console.error('停止音乐提醒失败:', data.error);
                showMessage('停止失败: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('停止音乐提醒请求失败:', error);
            showMessage('请求失败: ' + error, 'danger');
        });

        // 立即隐藏所有通知
        hideAllNotifications();
    }

    // 清除音乐事件
    function clearMusicEvent() {
        fetch('/api/clear-music-event', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('音乐事件已清除');
            } else {
                console.error('清除音乐事件失败:', data.error);
            }
        })
        .catch(error => {
            console.error('清除音乐事件请求失败:', error);
        });
    }

    // 清除弹窗事件
    function clearDialogEvent() {
        fetch('/api/clear-dialog-event', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('弹窗事件已清除');
            } else {
                console.error('清除弹窗事件失败:', data.error);
            }
        })
        .catch(error => {
            console.error('清除弹窗事件请求失败:', error);
        });
    }

    // 测试音乐播放功能
    function testMusicNotification() {
        const testBtn = document.getElementById('test-music-btn');
        const originalText = testBtn.innerHTML;

        // 显示加载状态
        testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中...';
        testBtn.disabled = true;

        console.log('开始测试音乐播放...');

        fetch('/api/test-music-notification', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            console.log('测试音乐播放响应:', data);

            if (data.success) {
                showMessage(`✅ ${data.message}`, 'success');
                if (data.info) {
                    setTimeout(() => {
                        showMessage(`💡 ${data.info}`, 'info');
                    }, 1000);
                }
                console.log('测试音乐播放成功:', data);
            } else {
                let errorMsg = `❌ ${data.error}`;
                if (data.suggestion) {
                    errorMsg += `\n💡 建议: ${data.suggestion}`;
                }
                showMessage(errorMsg, 'danger');
                console.error('测试音乐播放失败:', data);
            }
        })
        .catch(error => {
            console.error('测试音乐播放请求失败:', error);
            showMessage('❌ 测试请求失败，请检查网络连接', 'danger');
        })
        .finally(() => {
            // 恢复按钮状态
            testBtn.innerHTML = originalText;
            testBtn.disabled = false;
        });
    }

    // 页面刷新检测 - 自动停止音乐播放
    window.addEventListener('beforeunload', function(event) {
        // 如果有音乐正在播放，发送停止请求
        if (currentAudio && !currentAudio.paused) {
            // 使用同步请求确保在页面关闭前发送
            fetch('/api/stop-music-notification', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                keepalive: true  // 确保请求在页面关闭时也能发送
            }).catch(error => {
                console.log('停止音乐请求失败:', error);
            });
        }
    });

    console.log('🎵 音乐播放重构完成 - 页面刷新将自动停止音乐播放');
</script>
{% endblock %}
