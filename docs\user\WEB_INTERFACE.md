# Web管理界面使用说明

## 功能概述

LarkFlow Web管理界面提供了一个直观的Web界面，用于管理和监控飞书消息处理系统。通过浏览器即可实时查看系统状态、控制功能开关、修改配置参数。

## 访问地址

启动程序后，Web管理界面将自动启动，默认访问地址：
```
http://127.0.0.1:8080
```

## 主要功能

### 1. 状态概览页面 (/)

**实时监控**：
- 应用运行状态和运行时间
- WebSocket连接状态
- 消息接收和回复统计
- 音乐提醒和电话提醒统计

**功能开关控制**：
- 消息监听开关：控制是否接收飞书消息
- 自动回复开关：控制是否自动发送回复
- 音乐提醒开关：控制是否播放提醒音乐
- 电话提醒开关：控制是否发送电话提醒

**特点**：
- 实时数据更新（每3秒刷新）
- 一键开关控制
- 错误信息显示

### 2. 配置管理页面 (/config)

**配置分类**：
- **认证配置**：飞书Cookie等认证信息
- **消息配置**：消息匹配模式、自动回复内容
- **提醒配置**：音乐文件路径、弹窗标题等
- **电话配置**：飞书应用ID、密钥、用户ID等

**功能特性**：
- 分类显示配置项
- 实时配置预览
- 密码字段隐藏/显示切换
- 配置验证和保存
- 一键重置功能

### 3. 运行日志页面 (/logs)

**日志功能**：
- 实时日志显示
- 日志级别过滤（DEBUG、INFO、WARNING、ERROR、CRITICAL）
- 关键词搜索
- 显示行数控制
- 自动刷新开关

**特点**：
- 类似终端的日志显示
- 彩色日志级别标识
- 自动滚动到最新日志

## API接口

### 状态接口
```
GET /api/status
```
返回系统当前状态信息

### 开关控制接口
```
POST /api/toggle/<switch_name>
Content-Type: application/json
{
  "enabled": true/false
}
```
支持的开关：
- `message_listening`：消息监听
- `auto_reply`：自动回复
- `music_notification`：音乐提醒
- `phone_notification`：电话提醒

### 配置管理接口
```
GET /api/config          # 获取配置
POST /api/config         # 更新配置
```

### 重启接口
```
POST /api/restart
```

### 日志接口
```
GET /api/logs?lines=100
```

## 使用流程

### 1. 首次配置

1. 启动程序后访问 http://127.0.0.1:8080
2. 进入"配置管理"页面
3. 填写必需的配置项：
   - `LARK_COOKIE`：飞书Cookie（必须）
   - 其他配置项根据需要填写
4. 点击"保存配置"
5. 重启应用使配置生效

### 2. 日常使用

1. **状态监控**：在首页查看系统运行状态
2. **开关控制**：根据需要开启/关闭各项功能
3. **配置调整**：在配置页面修改参数
4. **日志查看**：在日志页面排查问题

### 3. 故障排除

1. **查看错误信息**：首页会显示最近的错误
2. **检查日志**：在日志页面搜索ERROR级别的日志
3. **验证配置**：确保必需的配置项已正确填写
4. **重启应用**：配置修改后可能需要重启

## 安全注意事项

1. **访问控制**：Web界面默认只监听本地地址(127.0.0.1)
2. **敏感信息**：Cookie和密钥等敏感信息请妥善保管
3. **网络安全**：如需远程访问，请配置适当的网络安全措施

## 技术特性

### 前端技术
- **Bootstrap 5**：响应式UI框架
- **Font Awesome**：图标库
- **原生JavaScript**：交互逻辑
- **AJAX**：异步数据更新

### 后端技术
- **Flask**：轻量级Web框架
- **多线程**：Web服务器独立线程运行
- **RESTful API**：标准化接口设计
- **实时更新**：WebSocket状态同步

### 响应式设计
- 支持桌面和移动设备
- 自适应布局
- 触摸友好的交互

## 扩展功能

### 自定义主题
可以通过修改 `app/web/static/style.css` 自定义界面样式

### 添加新页面
1. 在 `app/web/templates/` 添加HTML模板
2. 在 `app/web/web_server.py` 添加路由
3. 在侧边栏添加导航链接

### API扩展
在 `web_server.py` 中添加新的API路由，支持更多功能

## 常见问题

**Q: Web界面无法访问？**
A: 检查防火墙设置，确保8080端口未被占用

**Q: 配置保存后不生效？**
A: 某些配置需要重启应用才能生效

**Q: 开关控制无响应？**
A: 检查网络连接和浏览器控制台错误信息

**Q: 日志显示不完整？**
A: 调整显示行数或使用搜索功能过滤

## 更新日志

- v1.0.0：基础Web管理界面
- 支持状态监控、配置管理、日志查看
- 实现功能开关控制
- 响应式设计支持移动设备
