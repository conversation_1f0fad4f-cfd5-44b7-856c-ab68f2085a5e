#!/usr/bin/env python3
"""
文件移动脚本 - 整理项目目录结构
"""
import os
import shutil
from pathlib import Path

def copy_file_safe(src, dst):
    """安全复制文件"""
    try:
        src_path = Path(src)
        dst_path = Path(dst)

        if src_path.exists():
            # 确保目标目录存在
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(str(src_path), str(dst_path))
            print(f"✅ 复制: {src} -> {dst}")
            return True
        else:
            print(f"⚠️  源文件不存在: {src}")
            return False
    except Exception as e:
        print(f"❌ 复制失败: {src} -> {dst}, 错误: {str(e)}")
        return False

def move_file_safe(src, dst):
    """安全移动文件"""
    try:
        src_path = Path(src)
        dst_path = Path(dst)

        if src_path.exists():
            # 确保目标目录存在
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(src_path), str(dst_path))
            print(f"✅ 移动: {src} -> {dst}")
            return True
        else:
            print(f"⚠️  源文件不存在: {src}")
            return False
    except Exception as e:
        print(f"❌ 移动失败: {src} -> {dst}, 错误: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始整理项目目录结构...")
    
    # 创建目录
    directories = [
        "docs/user",
        "docs/developer", 
        "docs/guides",
        "scripts/maintenance",
        "scripts/tools"
    ]
    
    for dir_path in directories:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        print(f"📁 创建目录: {dir_path}")
    
    # 复制用户文档（WEB_INTERFACE.md已经移动过了）
    user_docs = [
        ("docs/MUSIC_NOTIFICATION.md", "docs/user/MUSIC_NOTIFICATION.md"),
        ("docs/PHONE_NOTIFICATION.md", "docs/user/PHONE_NOTIFICATION.md")
    ]

    print("\n📚 复制用户文档...")
    for src, dst in user_docs:
        copy_file_safe(src, dst)
    
    # 复制开发者文档
    dev_docs = [
        ("docs/DEVELOPMENT.md", "docs/developer/DEVELOPMENT.md"),
        ("docs/TROUBLESHOOTING.md", "docs/developer/TROUBLESHOOTING.md"),
        ("docs/SECURITY.md", "docs/developer/SECURITY.md"),
        ("docs/QUICK_FIX.md", "docs/developer/QUICK_FIX.md")
    ]

    print("\n🔧 复制开发者文档...")
    for src, dst in dev_docs:
        copy_file_safe(src, dst)
    
    # 移动指南文档
    guide_docs = [
        ("WEB_CONTROL_GUIDE.md", "docs/guides/WEB_CONTROL_GUIDE.md"),
        ("WEB_MUSIC_PLAYER_GUIDE.md", "docs/guides/WEB_MUSIC_PLAYER_GUIDE.md"),
        ("WEB_NOTIFICATION_DIALOG_GUIDE.md", "docs/guides/WEB_NOTIFICATION_DIALOG_GUIDE.md"),
        ("AUDIO_FILE_MANAGER_GUIDE.md", "docs/guides/AUDIO_FILE_MANAGER_GUIDE.md")
    ]

    print("\n📖 移动指南文档...")
    for src, dst in guide_docs:
        move_file_safe(src, dst)
    
    # 移动脚本文件
    script_moves = [
        ("quick_fix.py", "scripts/maintenance/quick_fix.py"),
        ("scripts/manual_phone_reminder.py", "scripts/tools/manual_phone_reminder.py"),
        ("scripts/setup_project.py", "scripts/tools/setup_project.py"),
        ("scripts/clean_cache.py", "scripts/maintenance/clean_cache.py")
    ]

    print("\n🔧 移动脚本文件...")
    for src, dst in script_moves:
        move_file_safe(src, dst)
    
    print("\n✅ 目录整理完成！")

if __name__ == "__main__":
    main()
