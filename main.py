"""
Lark Message Processor - Main Entry Point
Processes received Lark messages and sends replies.
"""
import sys
import asyncio
from loguru import logger

# 配置日志格式 - 简洁美观的输出格式
logger.remove()

# 添加控制台输出
logger.add(
    sys.stderr,
    format="<dim>{time:MM-DD HH:mm:ss}</dim> <level>[{level:^5}]</level> {message}",
    level="INFO"
)

# 添加文件输出
import os
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

logger.add(
    "logs/launcher.log",
    format="{time:YYYY-MM-DD HH:mm:ss} [{level:^5}] {message}",
    level="INFO",
    rotation="10 MB",
    retention="7 days",
    compression="zip"
)

async def main():
    """Main function to run the Lark message processor"""
    from app.api.auth import get_auth
    from app.api.lark_client import LarkClient
    from app.core.message_service import MessageService
    from app.web.state_manager import state_manager

    # 注意：Web管理界面现在由launcher.py管理
    # 如果直接运行main.py，将不会启动web界面
    logger.info("直接运行模式 - 不启动Web界面")
    logger.info("如需Web控制界面，请运行: python launcher.py")

    # 设置应用启动状态
    state_manager.start_application()

    # 同步配置文件的开关状态
    state_manager.sync_config_switches()

    logger.info("初始化认证...")
    try:
        auth = get_auth()
        logger.info("认证初始化成功.")
    except Exception as e:
        logger.error(f"认证初始化失败: {str(e)}")
        state_manager.set_error(f"认证初始化失败: {str(e)}")
        sys.exit(1)

    logger.info("创建 Lark 客户端...")
    try:
        lark_client = LarkClient(auth)
        logger.info("Lark 客户端创建成功.")
    except Exception as e:
        logger.error(f"Lark 客户端创建失败: {str(e)}")
        state_manager.set_error(f"Lark 客户端创建失败: {str(e)}")
        sys.exit(1)

    logger.info("创建消息服务...")
    message_service = MessageService(lark_client)

    try:
        logger.info("连接到 Lark WebSocket...")
        logger.info("开始接收消息...")
        logger.info('================================================================')

        # 尝试连接WebSocket
        await lark_client.connect_websocket(message_service.process_message)

        # 连接成功后设置状态
        state_manager.set_websocket_connected(True)
    except KeyboardInterrupt:
        logger.info("程序被用户中断.")
    except asyncio.CancelledError:
        logger.info("程序被取消.")
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        state_manager.set_error(str(e))
    finally:
        logger.info("程序正在退出...")
        state_manager.set_websocket_connected(False)
        state_manager.stop_application()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序被强制中断.")
    except Exception as e:
        logger.error(f"程序异常退出: {str(e)}")
    finally:
        sys.exit(0)