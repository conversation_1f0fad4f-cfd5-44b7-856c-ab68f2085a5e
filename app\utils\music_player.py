"""
音乐播放器模块
用于播放提醒音乐 - Web端播放模式
"""
import os
import threading
import time
from loguru import logger

class MusicPlayer:
    """音乐播放器类 - Web端播放模式"""

    def __init__(self):
        self.is_playing = False
        self.should_stop = False
        self.play_thread = None
        self.current_music_file = None
        logger.info("Web端音乐播放器初始化成功")

    def play_loop(self, music_file):
        """Web端播放音乐 - 通过状态管理器推送播放事件"""
        if not os.path.exists(music_file):
            logger.error(f"音乐文件不存在: {music_file}")
            return

        try:
            self.is_playing = True
            self.should_stop = False
            self.current_music_file = music_file

            logger.info(f"开始Web端音乐播放: {music_file}")

            # 推送播放开始事件到Web端
            self._push_play_event("start", music_file)

            # 保持播放状态直到停止
            while not self.should_stop:
                time.sleep(0.1)

        except Exception as e:
            logger.error(f"Web端音乐播放器出错: {str(e)}")
        finally:
            self.is_playing = False
            self.current_music_file = None
            # 推送播放停止事件到Web端
            self._push_play_event("stop", music_file)
            logger.info("Web端音乐播放已停止")

    def _push_play_event(self, action, music_file):
        """推送播放事件到Web端"""
        try:
            from app.web.state_manager import state_manager
            import os

            # 获取音频文件的相对路径用于Web访问
            relative_path = music_file.replace("\\", "/")

            # 如果是绝对路径，尝试转换为相对路径
            if os.path.isabs(music_file):
                # 获取当前工作目录
                cwd = os.getcwd().replace("\\", "/")
                if relative_path.startswith(cwd):
                    # 如果文件在工作目录下，转换为相对路径
                    relative_path = relative_path[len(cwd):].lstrip("/")
                else:
                    # 如果文件不在工作目录下，复制到static/audio目录
                    import shutil
                    filename = os.path.basename(music_file)
                    target_dir = "static/audio"
                    os.makedirs(target_dir, exist_ok=True)
                    target_path = os.path.join(target_dir, filename)

                    if not os.path.exists(target_path):
                        shutil.copy2(music_file, target_path)
                        logger.info(f"音频文件已复制到: {target_path}")

                    relative_path = f"static/audio/{filename}"

            if relative_path.startswith("./"):
                relative_path = relative_path[2:]

            event_data = {
                "action": action,  # "start" 或 "stop"
                "music_file": relative_path,
                "timestamp": time.time()
            }

            state_manager.set_music_play_event(event_data)
            logger.info(f"推送音乐播放事件: {action} - {relative_path}")

        except Exception as e:
            logger.error(f"推送播放事件失败: {str(e)}")

    def start_playing(self, music_file):
        """开始播放音乐（在新线程中）- 支持重复触发"""
        # 如果正在播放，先停止当前播放
        if self.is_playing:
            logger.info("停止当前播放，启动新的音乐播放")
            self.stop_playing()
            # 等待当前播放线程结束
            if self.play_thread and self.play_thread.is_alive():
                self.play_thread.join(timeout=1)

        # 启动新的播放线程
        self.play_thread = threading.Thread(
            target=self.play_loop,
            args=(music_file,),
            daemon=True
        )
        self.play_thread.start()
        logger.info(f"新的音乐播放线程已启动: {music_file}")

    def stop_playing(self):
        """停止播放音乐"""
        if not self.is_playing:
            return

        logger.info("正在停止Web端音乐播放...")
        self.should_stop = True

        # 推送停止事件到Web端
        if self.current_music_file:
            self._push_play_event("stop", self.current_music_file)

        # 等待播放线程结束
        if self.play_thread and self.play_thread.is_alive():
            self.play_thread.join(timeout=2)

    def is_music_playing(self):
        """检查音乐是否正在播放"""
        return self.is_playing

# 全局音乐播放器实例
music_player = MusicPlayer()
