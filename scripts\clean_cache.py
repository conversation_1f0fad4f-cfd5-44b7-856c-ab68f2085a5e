"""
清理项目缓存文件的脚本
"""
import os
import shutil
from pathlib import Path

def clean_pycache(root_dir):
    """清理__pycache__目录"""
    cleaned = []
    
    for root, dirs, files in os.walk(root_dir):
        if '__pycache__' in dirs:
            pycache_path = os.path.join(root, '__pycache__')
            try:
                shutil.rmtree(pycache_path)
                cleaned.append(pycache_path)
                print(f"✅ 已清理: {pycache_path}")
            except Exception as e:
                print(f"❌ 清理失败: {pycache_path} - {str(e)}")
    
    return cleaned

def clean_pyc_files(root_dir):
    """清理.pyc文件"""
    cleaned = []
    
    for root, dirs, files in os.walk(root_dir):
        for file in files:
            if file.endswith(('.pyc', '.pyo')):
                file_path = os.path.join(root, file)
                try:
                    os.remove(file_path)
                    cleaned.append(file_path)
                    print(f"✅ 已清理: {file_path}")
                except Exception as e:
                    print(f"❌ 清理失败: {file_path} - {str(e)}")
    
    return cleaned

def clean_logs(root_dir):
    """清理日志文件"""
    cleaned = []
    log_patterns = ['*.log', '*.log.*']
    
    for pattern in log_patterns:
        for log_file in Path(root_dir).rglob(pattern):
            try:
                log_file.unlink()
                cleaned.append(str(log_file))
                print(f"✅ 已清理日志: {log_file}")
            except Exception as e:
                print(f"❌ 清理日志失败: {log_file} - {str(e)}")
    
    return cleaned

def main():
    """主函数"""
    print("🧹 LarkFlow 项目缓存清理工具")
    print("=" * 50)
    
    # 获取项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    print(f"项目根目录: {project_root}")
    print()
    
    # 清理__pycache__目录
    print("🗂️ 清理 __pycache__ 目录...")
    pycache_cleaned = clean_pycache(project_root)
    
    # 清理.pyc文件
    print("\n📄 清理 .pyc/.pyo 文件...")
    pyc_cleaned = clean_pyc_files(project_root)
    
    # 清理日志文件
    print("\n📋 清理日志文件...")
    log_cleaned = clean_logs(project_root)
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 清理结果总结")
    print("=" * 50)
    print(f"__pycache__ 目录: {len(pycache_cleaned)} 个")
    print(f".pyc/.pyo 文件: {len(pyc_cleaned)} 个")
    print(f"日志文件: {len(log_cleaned)} 个")
    print(f"总计清理: {len(pycache_cleaned) + len(pyc_cleaned) + len(log_cleaned)} 个文件/目录")
    print("\n🎉 缓存清理完成！")

if __name__ == "__main__":
    main()
