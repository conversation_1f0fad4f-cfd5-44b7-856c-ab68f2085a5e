# 快速修复指南

## 🚨 常见问题快速解决

### 1. 音乐文件不存在

**问题现象**：
```
音乐文件不存在: D:\个人开发\Feishu_Windows_speak_text-main\dist\music.mp3
```

**快速解决**：

1. **方法一：使用正确的文件路径**
   - 将您的音乐文件复制到项目目录
   - 在Web界面配置页面修改音乐文件路径
   - 推荐路径：`D:\Personal\Downloads\zfb100w.mp3`

2. **方法二：使用项目默认路径**
   - 将音乐文件复制到 `static/audio/notification.mp3`
   - 在配置页面设置路径为：`static/audio/notification.mp3`

3. **方法三：创建默认音乐文件**
   ```bash
   # 创建音频目录
   mkdir -p static/audio
   
   # 复制您的音乐文件
   copy "D:\Personal\Downloads\zfb100w.mp3" "static/audio/notification.mp3"
   ```

### 2. Web配置修改不生效

**问题现象**：
- 在Web界面修改了配置
- 但程序仍使用旧配置

**快速解决**：

1. **检查配置保存状态**
   - 确保点击了"保存配置"按钮
   - 查看是否有成功提示

2. **重启应用程序**
   ```bash
   # 停止程序（Ctrl+C）
   python main.py
   ```

3. **检查.env文件**
   ```bash
   # 查看配置是否已保存
   type .env
   ```

### 3. 开关状态不同步

**问题现象**：
- Web界面显示开关已开启
- 但功能实际未启用

**快速解决**：

1. **重新启动程序**
   - 停止程序（Ctrl+C）
   - 重新运行：`python main.py`

2. **检查配置文件**
   - 确保.env文件中对应配置为 `true`
   - 例如：`ENABLE_MUSIC_NOTIFICATION=true`

3. **检查Web界面状态**
   - 访问 http://127.0.0.1:8080
   - 查看开关状态是否正确

### 4. 飞书Cookie失效

**问题现象**：
- WebSocket连接失败
- 无法接收消息

**快速解决**：

1. **重新获取Cookie**
   - 打开浏览器，登录飞书网页版
   - 按F12打开开发者工具
   - 在Network标签页找到请求，复制Cookie

2. **更新配置**
   - 在Web界面配置页面更新LARK_COOKIE
   - 保存配置并重启程序

### 5. 电话提醒不工作

**问题现象**：
- 电话提醒配置不完整
- 无法发送电话提醒

**快速解决**：

1. **完善配置**
   ```env
   LARK_APP_ID=your_app_id
   LARK_APP_SECRET=your_app_secret
   PHONE_NOTIFICATION_USER_IDS=user1,user2
   PHONE_NOTIFICATION_MESSAGE_IDS=msg1,msg2,msg3,msg4
   ```

2. **检查应用权限**
   - 确保飞书应用有发送消息权限
   - 验证用户ID和消息ID格式正确

## 🛠️ 一键修复脚本

创建 `quick_fix.py` 脚本：

```python
#!/usr/bin/env python3
"""
一键修复常见问题
"""
import os
import shutil

def fix_music_file():
    """修复音乐文件问题"""
    # 创建音频目录
    os.makedirs("static/audio", exist_ok=True)
    
    # 检查常见音乐文件位置
    possible_files = [
        "D:/Personal/Downloads/zfb100w.mp3",
        "music.mp3",
        "notification.mp3"
    ]
    
    for file_path in possible_files:
        if os.path.exists(file_path):
            target = "static/audio/notification.mp3"
            shutil.copy2(file_path, target)
            print(f"✅ 音乐文件已复制到: {target}")
            return True
    
    print("❌ 未找到音乐文件，请手动复制到 static/audio/notification.mp3")
    return False

def fix_config():
    """修复配置文件"""
    config_updates = {
        "NOTIFICATION_MUSIC_FILE": "static/audio/notification.mp3",
        "ENABLE_MUSIC_NOTIFICATION": "true",
        "ENABLE_PHONE_NOTIFICATION": "true"
    }
    
    # 读取现有配置
    env_content = ""
    if os.path.exists(".env"):
        with open(".env", "r", encoding="utf-8") as f:
            env_content = f.read()
    
    # 更新配置
    for key, value in config_updates.items():
        if key not in env_content:
            env_content += f"\n{key}={value}"
    
    # 保存配置
    with open(".env", "w", encoding="utf-8") as f:
        f.write(env_content)
    
    print("✅ 配置文件已更新")

if __name__ == "__main__":
    print("🔧 开始一键修复...")
    fix_music_file()
    fix_config()
    print("🎉 修复完成！请重启程序。")
```

## 📞 获取支持

如果问题仍未解决：

1. **查看详细日志**
   - 访问 http://127.0.0.1:8080/logs
   - 搜索ERROR级别的日志

2. **检查配置验证**
   - 访问 http://127.0.0.1:8080/config
   - 查看配置验证状态

3. **使用一键修复脚本**
   ```bash
   python quick_fix.py
   ```

4. **提交问题报告**
   - 包含完整的错误日志
   - 说明操作步骤
   - 提供环境信息
