# 更新日志

本文档记录了 LarkAgentX 项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增
- 项目结构整理和文档完善
- 完整的 README.md 文档
- MIT 许可证
- .gitignore 文件优化

## [1.2.0] - 2025-06-01

### 新增
- 🎵 音乐提醒功能
  - 支持 MP3、WAV、OGG 格式
  - 循环播放直到用户确认
  - 自定义弹窗提醒
- 📦 Windows EXE 打包功能
  - PyInstaller 自动打包
  - 一键打包批处理脚本
  - 依赖自动管理
- ⚙️ 配置系统优化
  - 音乐提醒相关配置
  - 环境变量管理

### 改进
- 🔧 日志系统优化
  - 移除冗余的模块路径信息
  - 简化日志输出格式
  - 保留关键操作日志
- 🚀 程序退出机制
  - 修复 Ctrl+C 无法退出的问题
  - 优雅的程序关闭流程
  - WebSocket 连接超时处理

### 修复
- 修复全局变量声明问题
- 修复 WebSocket 连接取消信号处理
- 修复音乐播放器初始化错误

## [1.1.0] - 2025-05-31

### 新增
- 🤖 智能消息处理
  - 正则表达式模式匹配
  - 自动回复功能
  - 群聊和私聊支持
- 🔐 飞书认证系统
  - Cookie 认证
  - 自动 CSRF 令牌获取
  - 用户信息获取

### 改进
- 📡 WebSocket 连接稳定性
- 🛠️ 协议解析优化
- 📝 日志系统完善

## [1.0.0] - 2025-05-30

### 新增
- 🚀 项目初始化
- 📡 飞书 WebSocket 连接
- 🔧 Protocol Buffers 协议解析
- 📨 消息接收和发送
- ⚙️ 基础配置系统

### 技术栈
- Python 3.10+
- asyncio/websockets
- Protocol Buffers
- loguru 日志系统

---

## 版本说明

### 版本号格式
- **主版本号**: 不兼容的 API 修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 变更类型
- **新增**: 新功能
- **改进**: 对现有功能的改进
- **修复**: 问题修复
- **移除**: 移除的功能
- **安全**: 安全相关的修复

### 发布周期
- **主版本**: 根据重大功能更新发布
- **次版本**: 每月发布（如有新功能）
- **修订版本**: 根据 bug 修复需要发布
