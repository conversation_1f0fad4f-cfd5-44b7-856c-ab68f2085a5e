# LarkAgentX Web控制指南

## 🌐 概述

LarkAgentX 现在支持通过Web界面控制整个应用的启动和停止，以及各个功能的开关管理。

## 🚀 快速开始

### 1. 启动Web控制器

```bash
python launcher.py
```

### 2. 访问Web界面

在浏览器中打开：`http://127.0.0.1:8080`

### 3. 配置参数

首次使用时，请先在"配置"页面设置：
- 飞书Cookie（必需）
- 消息匹配模式
- 自动回复内容
- 音乐文件路径
- 电话提醒配置

### 4. 验证Cookie（推荐）

在启动应用前，建议先点击"检查Cookie状态"按钮验证Cookie是否有效：
- ✅ **验证成功**: Cookie有效，可以正常启动应用
- ⚠️ **验证失败**: Cookie已过期或无效，需要更新

### 5. 启动应用

在首页点击"启动应用"按钮，LarkAgentX主功能将开始运行。

### 6. 控制功能

应用启动后，可以通过功能开关控制：
- 消息监听
- 自动回复
- 音乐提醒
- 电话提醒

## 📋 功能说明

### 应用控制
- **启动应用**: 启动LarkAgentX主功能，开始监听飞书消息
- **停止应用**: 停止主功能，断开WebSocket连接

### 功能开关
- **消息监听**: 控制是否接收飞书消息
- **自动回复**: 控制是否发送自动回复
- **音乐提醒**: 控制是否播放提醒音乐
- **电话提醒**: 控制是否发送电话提醒

### Cookie验证
- **检查Cookie状态**: 验证当前配置的飞书Cookie是否有效
- **智能错误提示**: 自动识别Cookie相关错误并提供解决建议
- **一键跳转**: Cookie错误时可直接跳转到配置页面更新

### 状态监控
- **运行状态**: 显示应用是否正在运行
- **WebSocket连接**: 显示与飞书的连接状态
- **消息统计**: 显示接收和回复的消息数量
- **提醒统计**: 显示音乐和电话提醒的触发次数

## 🔧 运行模式

### Web控制模式（推荐）
```bash
python launcher.py
```
- 启动Web控制器
- 通过浏览器控制应用启动/停止
- 实时监控状态和统计

### 直接运行模式
```bash
python main.py
```
- 直接启动主应用
- 不提供Web界面
- 适合命令行环境

## 🛡️ 安全特性

- **本地访问**: Web界面仅监听127.0.0.1，只能本地访问
- **敏感信息保护**: Cookie等敏感信息自动脱敏显示
- **安全输入**: 敏感字段使用密码输入框

## 📊 状态说明

### 应用状态
- **已停止**: 主应用未运行
- **运行中**: 主应用正在运行
- **启动中**: 正在启动主应用
- **停止中**: 正在停止主应用

### WebSocket状态
- **未连接**: 未连接到飞书服务器
- **已连接**: 已连接到飞书服务器，可以接收消息

## ⚠️ 注意事项

1. **配置优先**: 请先配置必要参数再启动应用
2. **单实例运行**: 同时只能运行一个主应用实例
3. **网络依赖**: 需要网络连接才能连接飞书服务器
4. **权限要求**: 音乐播放可能需要音频设备权限

## 🔍 故障排除

### 启动失败
1. 检查Python版本（需要3.8+）
2. 检查依赖包安装：`pip install -r requirements.txt`
3. 检查配置文件：确保.env文件存在且配置正确

### 连接失败
1. 检查网络连接
2. 检查飞书Cookie是否有效
3. 检查防火墙设置

### 应用无法停止问题
**已修复**: 当使用失效Cookie启动应用时，应用会自动检测错误并正确停止
- 应用遇到认证错误时会自动更新状态为"已停止"
- Web界面会实时反映应用的真实状态
- 错误信息会显示在界面上，帮助诊断问题

### 功能异常
1. 检查音乐文件是否存在
2. 检查电话提醒配置是否正确
3. 查看错误日志：logs/launcher.log

### 状态不同步
如果Web界面显示的状态与实际不符：
1. 刷新页面或等待自动更新（3秒）
2. 检查浏览器控制台是否有错误
3. 重启Web控制器

## 📝 日志文件

- **launcher.log**: Web控制器日志
- **app.log**: 主应用日志（如果存在）

## 🆘 获取帮助

如果遇到问题：
1. 查看Web界面的错误提示
2. 检查日志文件
3. 确认配置是否正确
4. 重启Web控制器

## 🔄 版本更新

更新后建议：
1. 重启Web控制器
2. 检查配置兼容性
3. 测试各项功能
