{% extends "base.html" %}

{% block page_title %}运行日志{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt"></i>
                    实时运行日志
                </h5>
                <div>
                    <button class="btn btn-secondary btn-sm" onclick="clearLogs()">
                        <i class="fas fa-trash"></i>
                        清空日志
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="refreshLogs()">
                        <i class="fas fa-sync"></i>
                        刷新
                    </button>
                    <div class="form-check form-switch d-inline-block ms-2">
                        <input class="form-check-input" type="checkbox" id="auto-refresh" checked>
                        <label class="form-check-label" for="auto-refresh">
                            自动刷新
                        </label>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="log-container p-3" id="log-container" style="height: 500px; overflow-y: auto;">
                    <div class="text-center text-muted">
                        <i class="fas fa-spinner fa-spin"></i>
                        加载日志中...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 日志过滤器 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-filter"></i>
                    日志过滤
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        <label for="log-level-filter" class="form-label">日志级别</label>
                        <select class="form-select" id="log-level-filter" onchange="filterLogs()">
                            <option value="">全部</option>
                            <option value="DEBUG">DEBUG</option>
                            <option value="INFO">INFO</option>
                            <option value="WARNING">WARNING</option>
                            <option value="ERROR">ERROR</option>
                            <option value="CRITICAL">CRITICAL</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="message-only-filter" class="form-label">内容类型</label>
                        <select class="form-select" id="message-only-filter" onchange="refreshLogs()">
                            <option value="false">全部日志</option>
                            <option value="true">仅显示消息</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <label for="log-search" class="form-label">搜索关键词</label>
                        <input type="text" class="form-control" id="log-search"
                               placeholder="输入关键词搜索日志..." onkeyup="filterLogs()">
                    </div>
                    <div class="col-md-3">
                        <label for="log-lines" class="form-label">显示行数</label>
                        <select class="form-select" id="log-lines" onchange="refreshLogs()">
                            <option value="50">最近50行</option>
                            <option value="100" selected>最近100行</option>
                            <option value="200">最近200行</option>
                            <option value="500">最近500行</option>
                            <option value="all">全部</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let allLogs = [];
    let filteredLogs = [];
    let autoRefreshInterval;
    
    document.addEventListener('DOMContentLoaded', function() {
        refreshLogs();
        startAutoRefresh();
        
        // 监听自动刷新开关
        document.getElementById('auto-refresh').addEventListener('change', function() {
            if (this.checked) {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
        });
    });
    
    function refreshLogs() {
        const lines = document.getElementById('log-lines').value;
        const messageOnly = document.getElementById('message-only-filter').value;

        fetch(`/api/logs?lines=${lines}&message_only=${messageOnly}`)
            .then(response => response.json())
            .then(data => {
                allLogs = data.logs || [];
                filterLogs();
            })
            .catch(error => {
                console.error('获取日志失败:', error);
                document.getElementById('log-container').innerHTML =
                    '<div class="text-danger"><i class="fas fa-exclamation-triangle"></i> 获取日志失败</div>';
            });
    }
    
    function filterLogs() {
        const levelFilter = document.getElementById('log-level-filter').value;
        const searchText = document.getElementById('log-search').value.toLowerCase();
        
        filteredLogs = allLogs.filter(log => {
            const levelMatch = !levelFilter || log.level === levelFilter;
            const textMatch = !searchText || 
                log.message.toLowerCase().includes(searchText) ||
                log.time.toLowerCase().includes(searchText);
            
            return levelMatch && textMatch;
        });
        
        displayLogs();
    }
    
    function displayLogs() {
        const container = document.getElementById('log-container');
        
        if (filteredLogs.length === 0) {
            container.innerHTML = '<div class="text-muted"><i class="fas fa-info-circle"></i> 暂无日志数据</div>';
            return;
        }
        
        let html = '';
        filteredLogs.forEach(log => {
            const levelClass = getLevelClass(log.level);
            const levelIcon = getLevelIcon(log.level);
            
            html += `
                <div class="log-entry mb-1">
                    <span class="text-muted">[${log.time}]</span>
                    <span class="badge ${levelClass} ms-2">
                        <i class="${levelIcon}"></i>
                        ${log.level}
                    </span>
                    <span class="ms-2">${escapeHtml(log.message)}</span>
                </div>
            `;
        });
        
        container.innerHTML = html;

        // 滚动到顶部（最新日志在上面）
        container.scrollTop = 0;
    }
    
    function getLevelClass(level) {
        const classes = {
            'DEBUG': 'bg-secondary',
            'INFO': 'bg-primary',
            'WARNING': 'bg-warning',
            'ERROR': 'bg-danger',
            'CRITICAL': 'bg-dark'
        };
        return classes[level] || 'bg-secondary';
    }
    
    function getLevelIcon(level) {
        const icons = {
            'DEBUG': 'fas fa-bug',
            'INFO': 'fas fa-info-circle',
            'WARNING': 'fas fa-exclamation-triangle',
            'ERROR': 'fas fa-times-circle',
            'CRITICAL': 'fas fa-skull-crossbones'
        };
        return icons[level] || 'fas fa-circle';
    }
    
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    function clearLogs() {
        if (confirm('确定要清空日志显示吗？')) {
            allLogs = [];
            filteredLogs = [];
            displayLogs();
            showMessage('日志显示已清空', 'info');
        }
    }
    
    function startAutoRefresh() {
        stopAutoRefresh(); // 先停止现有的定时器
        autoRefreshInterval = setInterval(refreshLogs, 5000); // 每5秒刷新一次
    }
    
    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
            autoRefreshInterval = null;
        }
    }
    
    // 页面卸载时停止自动刷新
    window.addEventListener('beforeunload', stopAutoRefresh);
</script>
{% endblock %}
