"""
项目初始化设置脚本
"""
import os
import shutil
from pathlib import Path

def create_env_file():
    """创建.env文件"""
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if env_file.exists():
        print("⚠️ .env 文件已存在，跳过创建")
        return False
    
    if env_example.exists():
        shutil.copy(env_example, env_file)
        print("✅ 已从 .env.example 创建 .env 文件")
        return True
    else:
        print("❌ .env.example 文件不存在")
        return False

def create_audio_directory():
    """创建音频目录"""
    audio_dir = Path("static/audio")
    
    if not audio_dir.exists():
        audio_dir.mkdir(parents=True, exist_ok=True)
        print("✅ 已创建 static/audio 目录")
    else:
        print("⚠️ static/audio 目录已存在")
    
    # 创建README文件
    readme_file = audio_dir / "README.md"
    if not readme_file.exists():
        readme_content = """# 音频文件目录

请将音乐提醒文件放置在此目录下。

## 支持的格式
- MP3
- WAV  
- OGG

## 建议的文件名
- notification.mp3 (默认)
- notification.wav
- alert.mp3
- alert.wav

## 文件要求
- 时长：建议 10-30 秒（会循环播放）
- 音量：适中，避免过于刺耳
- 大小：建议 5MB 以内
"""
        readme_file.write_text(readme_content, encoding='utf-8')
        print("✅ 已创建音频目录说明文件")

def create_logs_directory():
    """创建日志目录"""
    logs_dir = Path("logs")
    
    if not logs_dir.exists():
        logs_dir.mkdir(exist_ok=True)
        print("✅ 已创建 logs 目录")
    else:
        print("⚠️ logs 目录已存在")

def check_dependencies():
    """检查依赖包"""
    print("📦 检查依赖包...")
    
    required_packages = [
        'loguru',
        'protobuf', 
        'requests',
        'websockets',
        'python-dotenv',
        'pygame',
        'protobuf3-to-dict'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} (未安装)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    else:
        print("\n🎉 所有依赖包都已安装！")
        return True

def main():
    """主函数"""
    print("🚀 LarkAgentX 项目初始化设置")
    print("=" * 50)
    
    # 切换到项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    os.chdir(project_root)
    
    print(f"项目根目录: {project_root}")
    print()
    
    # 创建配置文件
    print("⚙️ 设置配置文件...")
    create_env_file()
    print()
    
    # 创建必要目录
    print("📁 创建必要目录...")
    create_audio_directory()
    create_logs_directory()
    print()
    
    # 检查依赖
    deps_ok = check_dependencies()
    print()
    
    # 总结
    print("=" * 50)
    print("📋 初始化完成！")
    print("=" * 50)
    
    print("📝 下一步操作：")
    print("1. 编辑 .env 文件，配置飞书 Cookie 和其他参数")
    print("2. 在 static/audio/ 目录放置音乐文件")
    print("3. 运行一键修复: python quick_fix.py")
    print("4. 启动程序: python main.py")
    
    if not deps_ok:
        print("\n⚠️ 请先安装缺少的依赖包！")

if __name__ == "__main__":
    main()
