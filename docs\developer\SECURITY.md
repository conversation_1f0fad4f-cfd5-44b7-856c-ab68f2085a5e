# 安全性说明

## 🔒 敏感信息保护

LarkFlow 采用多层安全措施保护您的敏感信息，确保Cookie、密钥等关键数据的安全性。

### 🛡️ 安全特性

#### 1. 敏感信息脱敏显示
- **自动识别**：系统自动识别敏感配置项
- **脱敏显示**：敏感信息在界面上以 `***已设置***` 或 `abc***xyz` 格式显示
- **不可查看**：一旦设置，无法通过任何方式查看原始值

#### 2. 敏感字段类型
系统自动保护以下类型的配置：
- `LARK_COOKIE` - 飞书Cookie
- `LARK_APP_SECRET` - 飞书应用密钥
- `OPENAI_API_KEY` - OpenAI API密钥
- 包含 `SECRET`、`PASSWORD`、`TOKEN`、`KEY`、`COOKIE` 关键词的字段

#### 3. 安全输入机制
- **密码字段**：敏感信息使用密码输入框
- **可见性切换**：支持临时显示/隐藏输入内容
- **占位符提示**：已设置的字段显示友好提示
- **覆盖机制**：输入新值可覆盖已有配置

### 🔐 技术实现

#### 1. 前端保护
```javascript
// 敏感字段不提交脱敏值
if (isSensitive && (value.includes('*') || value === '***已设置***')) {
    return; // 跳过未修改的敏感字段
}
```

#### 2. 后端脱敏
```python
def _mask_sensitive_value(self, value: str) -> str:
    """脱敏处理敏感值"""
    if not value or len(value) <= 8:
        return "***已设置***"
    return f"{value[:3]}{'*' * (len(value) - 6)}{value[-3:]}"
```

#### 3. 配置验证
- 使用原始值进行配置有效性验证
- 脱敏值仅用于显示，不影响功能

### 🎯 使用指南

#### 1. 首次配置
1. 在配置页面输入完整的敏感信息
2. 点击保存后，信息将被脱敏显示
3. 系统验证配置有效性

#### 2. 修改配置
1. 敏感字段显示为脱敏状态
2. 直接输入新值可覆盖原有配置
3. 留空则保持原有配置不变

#### 3. 安全提示
- ✅ 已设置的敏感信息无法被查看
- ✅ 配置文件中的值仍为明文（本地存储）
- ✅ Web界面传输时不包含敏感信息
- ⚠️ 请妥善保管配置文件访问权限

### 🔍 安全验证

#### 1. 配置状态检查
- 系统自动验证敏感配置的有效性
- 显示配置完整性状态
- 提供配置问题的修复建议

#### 2. 错误处理
- 配置错误时提供明确提示
- 不在错误信息中暴露敏感内容
- 引导用户正确配置

### 📋 最佳实践

#### 1. 配置管理
- 定期检查配置有效性
- 及时更新过期的Cookie和密钥
- 使用强密码和复杂密钥

#### 2. 访问控制
- 限制配置文件的访问权限
- 不要在公共场所操作敏感配置
- 定期备份重要配置

#### 3. 安全意识
- 不要截图包含敏感信息的界面
- 不要在日志中记录敏感信息
- 及时清理临时文件和缓存

### 🚨 安全注意事项

#### 1. 本地存储
- 配置文件（.env）中的敏感信息为明文存储
- 请确保文件系统的访问权限设置正确
- 建议使用文件加密或系统级权限控制

#### 2. 网络传输
- Web界面仅在本地访问（127.0.0.1）
- 敏感信息不会在网络中传输
- 如需远程访问，请配置安全的网络连接

#### 3. 内存安全
- 敏感信息在内存中的存储时间最小化
- 程序退出时自动清理敏感数据
- 避免内存转储泄露敏感信息

### 🔧 故障排除

#### 1. 配置无效
- 检查敏感信息是否正确输入
- 验证Cookie和密钥的有效期
- 确认配置格式符合要求

#### 2. 显示异常
- 刷新页面重新加载配置
- 检查浏览器控制台错误信息
- 重启应用程序重新初始化

#### 3. 安全问题
- 如发现安全漏洞，请及时报告
- 定期更新到最新版本
- 关注安全公告和更新说明

### 📞 安全支持

如果您发现安全相关问题：

1. **漏洞报告**：请通过安全渠道报告
2. **配置问题**：查看配置验证状态
3. **使用疑问**：参考本文档或联系支持

### 🔄 更新日志

- v2.0.0：实现敏感信息脱敏保护
- 支持自动识别敏感字段
- 添加配置安全验证
- 完善错误处理机制
