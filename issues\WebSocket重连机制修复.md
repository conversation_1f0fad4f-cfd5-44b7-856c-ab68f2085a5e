# WebSocket重连机制修复任务

## 问题描述
用户反馈WebSocket连接会自动关闭，重连机制不工作。手动启动应用可以成功，但自动重连失败。

## 问题分析
通过代码分析发现以下问题：
1. **双重重连机制冲突**：`lark_client.py`和`app_service_manager.py`都有各自的重连逻辑，相互干扰
2. **连接状态设置错误**：在连接尝试前就设置为已连接状态，而不是连接成功后
3. **事件循环管理混乱**：消息处理器的启动和停止时机不当

## 修复方案
采用简化重连机制方案：
- 移除`lark_client.py`中的重连逻辑，只保留单次连接
- 在`app_service_manager.py`中统一管理重连策略
- 修正连接状态设置时机
- 优化事件循环管理

## 修改内容

### 1. 修改 `app/api/lark_client.py`
- 移除`connect_websocket`方法中的重连循环逻辑
- 简化为单次连接尝试
- 保留心跳和消息处理逻辑
- 连接成功后才启动消息处理器

### 2. 修改 `app/core/app_service_manager.py`
- 修正`_websocket_connection_manager`中的状态设置时机
- 移除提前状态设置
- 只有在连接真正建立成功后才设置连接状态
- 优化错误处理和重连逻辑

### 3. 修改 `main.py`
- 修正连接状态设置时机
- 只有在连接成功后才设置为已连接状态

## 预期效果
- WebSocket连接更稳定
- 重连机制更可靠
- 状态管理更准确
- 解决自动关闭和重连失败的问题

## 测试计划
1. 测试手动启动功能
2. 测试自动重连机制
3. 确认状态管理的准确性
4. 验证连接稳定性

## 后续修复 - 富文本消息解析

### 问题描述
用户遇到错误："解析消息Frame/Packet失败: 'richText'"

### 问题分析
错误发生在`builder/proto.py`第205行，代码直接访问`TextContent['richText']['elements']['dictionary']`，但没有检查这些嵌套字段是否存在。

### 修复内容
1. 添加安全的字段访问检查
2. 支持多种富文本消息结构：
   - 优先从`innerText`提取内容
   - 其次从`elements.dictionary`解析
   - 最后从`text`字段获取
3. 增强错误处理和日志记录

### 修复效果
- 解决富文本消息解析失败问题
- 提高消息解析的健壮性
- 支持更多类型的富文本消息格式
