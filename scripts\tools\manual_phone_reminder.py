"""
手动电话提醒脚本
用于手动发送飞书紧急电话提醒
这是原始的123.py文件，重命名并移动到scripts目录
"""
import requests
import time

def main():
    """主函数"""
    print("🚀 手动发送飞书电话提醒")
    print("=" * 50)
    
    # 获取token
    print("正在获取访问令牌...")
    r1 = requests.post('https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal', 
        json={
            "app_id": "cli_a7b93233a3b6900e",
            "app_secret": "wUU06Pa2IviahCT2HS3mafcn6VBGqSQa"
        })

    if r1.status_code != 200:
        print(f"❌ 获取令牌失败: {r1.status_code} - {r1.text}")
        return

    token = r1.json()['tenant_access_token']
    print(f"✅ 成功获取令牌: {token[:20]}...")

    # 消息ID列表
    message_ids = [
        "om_x100b4cefd17554b80f375c73e7e0a75",
        "om_x100b4ce84bdde8b40ec5d5a93d6a033", 
        "om_x100b4ce8480fa4a00e3f17a92b545c2",
        "om_x100b4ce8464628a40f3c9e16e8f0bee"
    ]

    # 目标用户ID
    user_ids = ["on_585f5d1bb83c14b82f1b202c2c7ea12d"]

    print(f"\n开始发送 {len(message_ids)} 个电话提醒...")
    print(f"目标用户: {user_ids}")
    print("=" * 50)

    # 连续发送4个请求，间隔15秒
    for i, msg_id in enumerate(message_ids, 1):
        print(f"\n发送第 {i}/{len(message_ids)} 个提醒...")
        print(f"消息ID: {msg_id}")
        
        r = requests.patch(
            f'https://open.feishu.cn/open-apis/im/v1/messages/{msg_id}/urgent_phone?user_id_type=union_id',
            headers={'Authorization': f'Bearer {token}'},
            json={"user_id_list": user_ids}
        )
        
        if r.status_code == 200:
            print(f"✅ 请求{i} 发送成功: {r.status_code}")
        else:
            print(f"❌ 请求{i} 发送失败: {r.status_code} - {r.text}")
        
        # 如果不是最后一个请求，等待15秒
        if i < len(message_ids):
            print(f"⏳ 等待15秒后发送下一个...")
            time.sleep(15)

    print("\n" + "=" * 50)
    print("🎉 所有电话提醒发送完成！")

if __name__ == "__main__":
    main()
